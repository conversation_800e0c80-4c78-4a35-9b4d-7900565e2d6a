<?php
// Site Configuration
define('SITE_NAME', 'South Safari');
define('SITE_URL', 'http://localhost/southsafari');
define('SITE_EMAIL', '<EMAIL>');
define('ADMIN_EMAIL', '<EMAIL>');

// WhatsApp Configuration
define('WHATSAPP_NUMBER', '+27 67 975 7128');
define('WHATSAPP_LINK', 'https://wa.me/' . str_replace('+', '', WHATSAPP_NUMBER));

// File Upload Configuration
define('UPLOAD_DIR', 'uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB

// Pagination
define('POSTS_PER_PAGE', 10);
define('PROJECTS_PER_PAGE', 12);

// Security
define('ADMIN_SESSION_TIMEOUT', 3600); // 1 hour

// Database Configuration
require_once 'database.php';

// Helper Functions
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

function redirect($url) {
    header("Location: " . $url);
    exit();
}

function is_admin_logged_in() {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
}

function format_date($date) {
    return date('F j, Y', strtotime($date));
}

function truncate_text($text, $length = 150) {
    if (strlen($text) <= $length) {
        return $text;
    }
    return substr($text, 0, $length) . '...';
}
?>
