<?php
// Determine the correct path to config based on where this file is called from
$config_path = file_exists('../config/config.php') ? '../config/config.php' : 'config/config.php';
require_once $config_path;

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Sanitize and validate input
    $project_id = intval($_POST['project_id'] ?? 0);
    $name = sanitize_input($_POST['name'] ?? '');
    $email = sanitize_input($_POST['email'] ?? '');
    $country = sanitize_input($_POST['country'] ?? '');
    $experience_level = sanitize_input($_POST['experience_level'] ?? '');
    $team_overview = sanitize_input($_POST['team_overview'] ?? '');
    $project_experience = sanitize_input($_POST['project_experience'] ?? '');
    $development_approach = sanitize_input($_POST['development_approach'] ?? '');
    $team_capabilities = sanitize_input($_POST['team_capabilities'] ?? '');
    $portfolio_url = sanitize_input($_POST['portfolio_url'] ?? '');
    $message = sanitize_input($_POST['message'] ?? '');
    
    // Validation
    $errors = [];
    
    if ($project_id <= 0) {
        $errors[] = 'Invalid project ID';
    }
    
    if (empty($name)) {
        $errors[] = 'Name is required';
    }
    
    if (empty($email)) {
        $errors[] = 'Email is required';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Invalid email format';
    }
    
    if (empty($country)) {
        $errors[] = 'Country is required';
    }
    
    if (empty($experience_level)) {
        $errors[] = 'Experience level is required';
    }

    if (empty($team_overview)) {
        $errors[] = 'Team/Organization Overview is required';
    }

    if (empty($project_experience)) {
        $errors[] = 'Previous Project Experience is required';
    }

    if (empty($development_approach)) {
        $errors[] = 'Development Approach is required';
    }

    if (empty($team_capabilities)) {
        $errors[] = 'Team Size & Capabilities is required';
    }

    if (empty($message)) {
        $errors[] = 'Message is required';
    }
    
    if (!empty($portfolio_url) && !filter_var($portfolio_url, FILTER_VALIDATE_URL)) {
        $errors[] = 'Invalid portfolio URL format';
    }
    
    if (!empty($errors)) {
        echo json_encode(['success' => false, 'message' => implode(', ', $errors)]);
        exit;
    }
    
    $database = new Database();
    $db = $database->getConnection();
    
    // Verify project exists and is seeking developers
    $projectQuery = "SELECT id, title FROM projects WHERE id = ? AND status = 'seeking'";
    $projectStmt = $db->prepare($projectQuery);
    $projectStmt->execute([$project_id]);
    $project = $projectStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$project) {
        echo json_encode(['success' => false, 'message' => 'Project not found or no longer accepting applications']);
        exit;
    }
    
    // Handle file upload if present
    $cv_filename = null;
    if (isset($_FILES['cv_file']) && $_FILES['cv_file']['error'] === UPLOAD_ERR_OK) {
        $cv_filename = handleFileUpload($_FILES['cv_file']);
        if (!$cv_filename) {
            echo json_encode(['success' => false, 'message' => 'Error uploading CV file']);
            exit;
        }
    }
    
    // Check if developer already exists, if not create new record
    $developerQuery = "SELECT id FROM developers WHERE email = ?";
    $developerStmt = $db->prepare($developerQuery);
    $developerStmt->execute([$email]);
    $developer = $developerStmt->fetch(PDO::FETCH_ASSOC);
    
    if ($developer) {
        $developer_id = $developer['id'];
        
        // Update existing developer record
        $updateQuery = "UPDATE developers SET name = ?, country = ?, experience_level = ?, portfolio_url = ?, cv_file = COALESCE(?, cv_file), updated_at = NOW() WHERE id = ?";
        $updateStmt = $db->prepare($updateQuery);
        $updateStmt->execute([$name, $country, $experience_level, $portfolio_url, $cv_filename, $developer_id]);
    } else {
        // Create new developer record
        $insertQuery = "INSERT INTO developers (name, email, country, experience_level, portfolio_url, cv_file, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())";
        $insertStmt = $db->prepare($insertQuery);
        $insertStmt->execute([$name, $email, $country, $experience_level, $portfolio_url, $cv_filename]);
        $developer_id = $db->lastInsertId();
    }
    
    // Check if application already exists for this project and developer
    $existingQuery = "SELECT id FROM project_applications WHERE project_id = ? AND developer_id = ?";
    $existingStmt = $db->prepare($existingQuery);
    $existingStmt->execute([$project_id, $developer_id]);
    
    if ($existingStmt->fetch()) {
        echo json_encode(['success' => false, 'message' => 'You have already applied for this project']);
        exit;
    }
    
    // Create project application
    $applicationQuery = "INSERT INTO project_applications (project_id, developer_id, message, team_overview, project_experience, development_approach, team_capabilities, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
    $applicationStmt = $db->prepare($applicationQuery);
    $applicationStmt->execute([$project_id, $developer_id, $message, $team_overview, $project_experience, $development_approach, $team_capabilities]);
    
    // Send notification email (disabled for local development)
    // $emailSent = sendApplicationNotification($project['title'], $name, $email, $message);
    
    echo json_encode([
        'success' => true, 
        'message' => 'Your application has been submitted successfully! We will review it and get back to you soon.'
    ]);
    
} catch (Exception $e) {
    error_log("Application form error: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'An error occurred while submitting your application. Please try again or contact us directly.'
    ]);
}

function handleFileUpload($file) {
    // Create uploads directory if it doesn't exist
    $uploadDir = '../' . UPLOAD_DIR;
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    // Validate file
    $allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    $maxSize = MAX_FILE_SIZE;
    
    if (!in_array($file['type'], $allowedTypes)) {
        return false;
    }
    
    if ($file['size'] > $maxSize) {
        return false;
    }
    
    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'cv_' . uniqid() . '.' . $extension;
    $filepath = $uploadDir . $filename;
    
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return $filename;
    }
    
    return false;
}

function sendApplicationNotification($projectTitle, $name, $email, $message) {
    $to = ADMIN_EMAIL;
    $subject = "New Project Application: " . $projectTitle;
    
    $emailBody = "
    New project application received:
    
    Project: $projectTitle
    Applicant: $name
    Email: $email
    
    Application Message:
    $message
    
    ---
    Submitted at: " . date('Y-m-d H:i:s') . "
    
    Please review the application in the admin dashboard.
    ";
    
    $headers = "From: " . SITE_EMAIL . "\r\n";
    $headers .= "Reply-To: $email\r\n";
    $headers .= "X-Mailer: PHP/" . phpversion();
    
    return mail($to, $subject, $emailBody, $headers);
}
?>
