<?php
require_once 'config/config.php';

// Get project ID from URL
$project_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$project_id) {
    header('Location: projects.php');
    exit;
}

// Get project details from database
try {
    $database = new Database();
    $db = $database->getConnection();
    
    $stmt = $db->prepare("SELECT * FROM projects WHERE id = ? AND status = 'seeking'");
    $stmt->execute([$project_id]);
    $project = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$project) {
        header('Location: projects.php');
        exit;
    }
    
} catch(Exception $e) {
    header('Location: projects.php');
    exit;
}

$page_title = $project['title'];
$page_description = truncate_text($project['description'], 150);

include 'includes/header.php';
?>

<style>
/* Project Detail Page Styles */
.project-detail-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
    background: #f8fafc;
    min-height: 100vh;
}

.back-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    margin-bottom: 2rem;
    padding: 0.75rem 1.5rem;
    background: white;
    border-radius: 50px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1);
    transition: all 0.3s ease;
}

.back-link:hover {
    color: #5a6fd8;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.15);
}

.project-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 3rem;
    border-radius: 20px;
    margin-bottom: 3rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.project-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.project-header > * {
    position: relative;
    z-index: 1;
}

.project-status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255,255,255,0.25);
    backdrop-filter: blur(10px);
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-weight: 600;
    margin-bottom: 1.5rem;
    border: 1px solid rgba(255,255,255,0.2);
}

.project-header h1 {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    line-height: 1.1;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.project-meta {
    display: flex;
    justify-content: center;
    gap: 3rem;
    flex-wrap: wrap;
    font-size: 1rem;
    opacity: 0.95;
    font-weight: 500;
}

.project-meta span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.project-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    margin-bottom: 4rem;
}

@media (max-width: 968px) {
    .project-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .project-header {
        padding: 3rem 2rem;
    }

    .project-header h1 {
        font-size: 2.5rem;
    }

    .project-meta {
        gap: 1.5rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .project-detail-container {
        padding: 1rem 0.5rem;
    }

    .project-header {
        padding: 2rem 1.5rem;
        margin-bottom: 2rem;
    }

    .project-header h1 {
        font-size: 2rem;
    }

    .project-meta {
        flex-direction: column;
        gap: 1rem;
    }
}

.main-content {
    background: white;
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.08);
    border: 1px solid rgba(0,0,0,0.05);
}

.main-content h2 {
    color: #1a202c;
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 3px solid #667eea;
    display: inline-block;
}

.main-content h3 {
    color: #2d3748;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 2.5rem 0 1.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.main-content h3::before {
    content: '';
    width: 4px;
    height: 24px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 2px;
}

.project-description {
    line-height: 1.8;
    color: #4a5568;
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

.partnership-terms {
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    border: 1px solid #e2e8f0;
    border-left: 5px solid #667eea;
    padding: 2rem;
    border-radius: 15px;
    margin: 2.5rem 0;
    position: relative;
}

.partnership-terms::before {
    content: '💼';
    position: absolute;
    top: -10px;
    left: 20px;
    background: white;
    padding: 0.5rem;
    border-radius: 50%;
    font-size: 1.2rem;
}

.partnership-terms h3 {
    color: #667eea;
    margin-bottom: 1rem;
    margin-top: 0;
}

.partnership-terms h3::before {
    display: none;
}

.requirements-list {
    color: #4a5568;
    line-height: 1.8;
    padding-left: 0;
    list-style: none;
}

.requirements-list li {
    padding: 0.75rem 0;
    padding-left: 2rem;
    position: relative;
    border-bottom: 1px solid #e2e8f0;
}

.requirements-list li:last-child {
    border-bottom: none;
}

.requirements-list li::before {
    content: '✓';
    position: absolute;
    left: 0;
    top: 0.75rem;
    color: #48bb78;
    font-weight: bold;
    font-size: 1.1rem;
}

.sidebar {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.info-card {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.08);
    border: 1px solid rgba(0,0,0,0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.info-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 50px rgba(0,0,0,0.12);
}

.info-card h3 {
    color: #667eea;
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.info-card h3 i {
    font-size: 1.1rem;
}

.project-details-list {
    color: #4a5568;
    line-height: 1.8;
}

.project-details-list p {
    margin-bottom: 1rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f5f9;
}

.project-details-list p:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.project-details-list strong {
    color: #2d3748;
    font-weight: 600;
    display: inline-block;
    min-width: 120px;
}

.cta-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2.5rem;
    border-radius: 20px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    pointer-events: none;
}

.cta-section > * {
    position: relative;
    z-index: 1;
}

.cta-section h3 {
    color: white;
    margin-bottom: 1rem;
    font-size: 1.5rem;
    font-weight: 700;
}

.cta-section p {
    color: rgba(255,255,255,0.9);
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.btn-large {
    padding: 1.25rem 2.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    margin: 0.75rem 0.5rem;
    min-width: 220px;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
}

.btn-large.btn-primary {
    background: white;
    color: #667eea;
}

.btn-large.btn-primary:hover {
    background: #f7fafc;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.btn-large.btn-secondary {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
}

.btn-large.btn-secondary:hover {
    background: rgba(255,255,255,0.3);
    border-color: rgba(255,255,255,0.5);
    transform: translateY(-2px);
}
</style>

<!-- Back to Projects -->
<div class="project-detail-container">
    <a href="projects.php" class="back-link">
        <i class="fas fa-arrow-left"></i>
        Back to All Projects
    </a>

    <!-- Project Header -->
    <div class="project-header">
        <div class="project-status-badge">
            <i class="fas fa-circle" style="color: #28a745; font-size: 0.5rem;"></i>
            Seeking Developers
        </div>
        <h1><?php echo htmlspecialchars($project['title']); ?></h1>
        <div class="project-meta">
            <span><i class="fas fa-calendar"></i> Posted <?php echo format_date($project['created_at']); ?></span>
            <span><i class="fas fa-eye"></i> Project ID: #<?php echo $project['id']; ?></span>
        </div>
    </div>

    <!-- Project Content -->
    <div class="project-content">
        <div class="main-content">
            <h2><i class="fas fa-file-alt"></i> Project Description</h2>
            <div class="project-description">
                <?php echo nl2br(htmlspecialchars($project['description'])); ?>
            </div>

            <div class="partnership-terms">
                <h3>
                    <i class="fas fa-handshake"></i> Partnership Terms
                </h3>
                <p style="margin: 0; color: #4a5568; line-height: 1.7; font-size: 1.05rem;">
                    <?php echo nl2br(htmlspecialchars($project['partnership_terms'])); ?>
                </p>
            </div>

            <h3><i class="fas fa-search"></i> What We're Looking For</h3>
            <ul class="requirements-list">
                <li>Experienced development teams with proven track record</li>
                <li>Strong communication skills and English proficiency</li>
                <li>Ability to work in South African time zones when needed</li>
                <li>Commitment to long-term partnership success</li>
                <li>Portfolio demonstrating relevant project experience</li>
                <li>Professional approach to project management and delivery</li>
            </ul>
        </div>

        <div class="sidebar">
            <div class="info-card">
                <h3><i class="fas fa-info-circle"></i> Project Details</h3>
                <div class="project-details-list">
                    <p><strong>Status:</strong> Actively Seeking Partners</p>
                    <p><strong>Timeline:</strong> 3-6 months typical</p>
                    <p><strong>Partnership Type:</strong> Revenue Sharing</p>
                    <p><strong>Market:</strong> South African Focus</p>
                    <p><strong>Posted:</strong> <?php echo format_date($project['created_at']); ?></p>
                    <p><strong>Project ID:</strong> #<?php echo $project['id']; ?></p>
                </div>
            </div>

            <div class="cta-section">
                <h3><i class="fas fa-rocket"></i> Ready to Apply?</h3>
                <p>Join this exciting project and build your success story with us. We're looking for dedicated development partners.</p>
                <button class="btn btn-primary btn-large"
                        onclick="openApplicationModal(<?php echo $project['id']; ?>, '<?php echo htmlspecialchars($project['title']); ?>')">
                    <i class="fas fa-paper-plane"></i> Apply Now
                </button>
                <br>
                <a href="contact.php" class="btn btn-secondary btn-large">
                    <i class="fas fa-comments"></i> Ask Questions
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Application Modal -->
<div id="application-modal" class="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.6); backdrop-filter: blur(5px); z-index: 1000; overflow-y: auto;">
    <div class="modal-content" style="background: white; margin: 2% auto; padding: 0; width: 95%; max-width: 700px; border-radius: 20px; position: relative; box-shadow: 0 20px 60px rgba(0,0,0,0.3); border: 1px solid rgba(0,0,0,0.1);">
        <div class="modal-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 2rem; border-radius: 20px 20px 0 0; position: relative;">
            <span class="close" onclick="closeApplicationModal()" style="position: absolute; top: 1rem; right: 1rem; font-size: 1.8rem; cursor: pointer; z-index: 2; color: white; background: rgba(255,255,255,0.2); width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;">&times;</span>
            <h2 id="modal-title" style="margin: 0; font-size: 1.8rem; font-weight: 700;">Apply for Project</h2>
            <p style="margin: 0.5rem 0 0 0; opacity: 0.9; font-size: 1.1rem;">Fill out the form below to apply for this partnership opportunity.</p>
        </div>
        <div style="padding: 2.5rem;">
        
        <h2 id="modal-title">Apply for Project</h2>
        <p style="color: #666; margin-bottom: 2rem;">Fill out the form below to apply for this partnership opportunity.</p>
        
        <form id="application-form" class="application-form" onsubmit="submitApplicationForm(this); return false;">
            <input type="hidden" id="project-id" name="project_id">

            <div class="form-row" style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1.5rem;">
                <div>
                    <label for="applicant-name" style="display: block; margin-bottom: 0.75rem; font-weight: 600; color: #2d3748;">Full Name *</label>
                    <input type="text" id="applicant-name" name="name" required
                           style="width: 100%; padding: 1rem; border: 2px solid #e2e8f0; border-radius: 10px; font-size: 1rem; transition: border-color 0.3s ease; background: #f7fafc;"
                           onfocus="this.style.borderColor='#667eea'; this.style.background='white';"
                           onblur="this.style.borderColor='#e2e8f0'; this.style.background='#f7fafc';">
                </div>

                <div>
                    <label for="applicant-email" style="display: block; margin-bottom: 0.75rem; font-weight: 600; color: #2d3748;">Email Address *</label>
                    <input type="email" id="applicant-email" name="email" required
                           style="width: 100%; padding: 1rem; border: 2px solid #e2e8f0; border-radius: 10px; font-size: 1rem; transition: border-color 0.3s ease; background: #f7fafc;"
                           onfocus="this.style.borderColor='#667eea'; this.style.background='white';"
                           onblur="this.style.borderColor='#e2e8f0'; this.style.background='#f7fafc';">
                </div>
            </div>

            <div class="form-row" style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1.5rem;">
                <div>
                    <label for="applicant-country" style="display: block; margin-bottom: 0.75rem; font-weight: 600; color: #2d3748;">Country *</label>
                    <select id="applicant-country" name="country" required
                            style="width: 100%; padding: 1rem; border: 2px solid #e2e8f0; border-radius: 10px; font-size: 1rem; transition: border-color 0.3s ease; background: #f7fafc;"
                            onfocus="this.style.borderColor='#667eea'; this.style.background='white';"
                            onblur="this.style.borderColor='#e2e8f0'; this.style.background='#f7fafc';">
                        <option value="">Select your country</option>
                        <option value="Pakistan">Pakistan</option>
                        <option value="India">India</option>
                        <option value="Bangladesh">Bangladesh</option>
                        <option value="Sri Lanka">Sri Lanka</option>
                        <option value="Nepal">Nepal</option>
                        <option value="Other">Other</option>
                    </select>
                </div>

                <div>
                    <label for="experience-level" style="display: block; margin-bottom: 0.75rem; font-weight: 600; color: #2d3748;">Experience Level *</label>
                    <select id="experience-level" name="experience_level" required
                            style="width: 100%; padding: 1rem; border: 2px solid #e2e8f0; border-radius: 10px; font-size: 1rem; transition: border-color 0.3s ease; background: #f7fafc;"
                            onfocus="this.style.borderColor='#667eea'; this.style.background='white';"
                            onblur="this.style.borderColor='#e2e8f0'; this.style.background='#f7fafc';">
                        <option value="">Select experience level</option>
                        <option value="junior">Junior (1-2 years)</option>
                        <option value="mid">Mid-level (3-5 years)</option>
                        <option value="senior">Senior (5+ years)</option>
                        <option value="expert">Expert (10+ years)</option>
                    </select>
                </div>
            </div>

            <div style="margin-bottom: 1.5rem;">
                <label for="team-overview" style="display: block; margin-bottom: 0.75rem; font-weight: 600; color: #2d3748;">Team/Organization Overview *</label>
                <textarea id="team-overview" name="team_overview" required rows="4"
                          placeholder="Brief description of your development team/company..."
                          style="width: 100%; padding: 1rem; border: 2px solid #e2e8f0; border-radius: 10px; resize: vertical; font-size: 1rem; line-height: 1.5; transition: border-color 0.3s ease; background: #f7fafc;"
                          onfocus="this.style.borderColor='#667eea'; this.style.background='white';"
                          onblur="this.style.borderColor='#e2e8f0'; this.style.background='#f7fafc';"></textarea>
            </div>

            <div style="margin-bottom: 1.5rem;">
                <label for="project-experience" style="display: block; margin-bottom: 0.75rem; font-weight: 600; color: #2d3748;">Previous Project Experience *</label>
                <textarea id="project-experience" name="project_experience" required rows="4"
                          placeholder="Examples of similar projects you've completed..."
                          style="width: 100%; padding: 1rem; border: 2px solid #e2e8f0; border-radius: 10px; resize: vertical; font-size: 1rem; line-height: 1.5; transition: border-color 0.3s ease; background: #f7fafc;"
                          onfocus="this.style.borderColor='#667eea'; this.style.background='white';"
                          onblur="this.style.borderColor='#e2e8f0'; this.style.background='#f7fafc';"></textarea>
            </div>

            <div style="margin-bottom: 1.5rem;">
                <label for="development-approach" style="display: block; margin-bottom: 0.75rem; font-weight: 600; color: #2d3748;">Development Approach *</label>
                <textarea id="development-approach" name="development_approach" required rows="4"
                          placeholder="How you typically handle projects from start to finish..."
                          style="width: 100%; padding: 1rem; border: 2px solid #e2e8f0; border-radius: 10px; resize: vertical; font-size: 1rem; line-height: 1.5; transition: border-color 0.3s ease; background: #f7fafc;"
                          onfocus="this.style.borderColor='#667eea'; this.style.background='white';"
                          onblur="this.style.borderColor='#e2e8f0'; this.style.background='#f7fafc';"></textarea>
            </div>

            <div style="margin-bottom: 1.5rem;">
                <label for="team-capabilities" style="display: block; margin-bottom: 0.75rem; font-weight: 600; color: #2d3748;">Team Size & Capabilities *</label>
                <textarea id="team-capabilities" name="team_capabilities" required rows="4"
                          placeholder="Size of your team and what you can deliver..."
                          style="width: 100%; padding: 1rem; border: 2px solid #e2e8f0; border-radius: 10px; resize: vertical; font-size: 1rem; line-height: 1.5; transition: border-color 0.3s ease; background: #f7fafc;"
                          onfocus="this.style.borderColor='#667eea'; this.style.background='white';"
                          onblur="this.style.borderColor='#e2e8f0'; this.style.background='#f7fafc';"></textarea>
            </div>

            <div style="margin-bottom: 1.5rem;">
                <label for="portfolio-url" style="display: block; margin-bottom: 0.75rem; font-weight: 600; color: #2d3748;">Portfolio URL</label>
                <input type="url" id="portfolio-url" name="portfolio_url"
                       placeholder="https://your-portfolio.com"
                       style="width: 100%; padding: 1rem; border: 2px solid #e2e8f0; border-radius: 10px; font-size: 1rem; transition: border-color 0.3s ease; background: #f7fafc;"
                       onfocus="this.style.borderColor='#667eea'; this.style.background='white';"
                       onblur="this.style.borderColor='#e2e8f0'; this.style.background='#f7fafc';">
            </div>

            <div style="margin-bottom: 1.5rem;">
                <label for="application-message" style="display: block; margin-bottom: 0.75rem; font-weight: 600; color: #2d3748;">Why are you interested in this project? *</label>
                <textarea id="application-message" name="message" required rows="5"
                          placeholder="Tell us why you're the right fit for this project..."
                          style="width: 100%; padding: 1rem; border: 2px solid #e2e8f0; border-radius: 10px; resize: vertical; font-size: 1rem; line-height: 1.5; transition: border-color 0.3s ease; background: #f7fafc;"
                          onfocus="this.style.borderColor='#667eea'; this.style.background='white';"
                          onblur="this.style.borderColor='#e2e8f0'; this.style.background='#f7fafc';"></textarea>
            </div>

            <div style="margin-bottom: 2rem;">
                <label for="cv-file" style="display: block; margin-bottom: 0.75rem; font-weight: 600; color: #2d3748;">Upload CV/Resume</label>
                <input type="file" id="cv-file" name="cv_file" accept=".pdf,.doc,.docx"
                       style="width: 100%; padding: 1rem; border: 2px solid #e2e8f0; border-radius: 10px; font-size: 1rem; transition: border-color 0.3s ease; background: #f7fafc;"
                       onfocus="this.style.borderColor='#667eea'; this.style.background='white';"
                       onblur="this.style.borderColor='#e2e8f0'; this.style.background='#f7fafc';">
                <small style="color: #718096; margin-top: 0.5rem; display: block;">Accepted formats: PDF, DOC, DOCX (Max 5MB)</small>
            </div>

            <div style="display: flex; gap: 1rem; justify-content: flex-end; padding-top: 1rem; border-top: 2px solid #f1f5f9;">
                <button type="button" onclick="closeApplicationModal()"
                        style="padding: 1rem 2rem; border: 2px solid #e2e8f0; background: white; color: #4a5568; border-radius: 10px; font-weight: 600; cursor: pointer; transition: all 0.3s ease; min-width: 120px;"
                        onmouseover="this.style.background='#f7fafc'; this.style.borderColor='#cbd5e0';"
                        onmouseout="this.style.background='white'; this.style.borderColor='#e2e8f0';">
                    Cancel
                </button>
                <button type="submit"
                        style="padding: 1rem 2rem; border: none; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 10px; font-weight: 600; cursor: pointer; transition: all 0.3s ease; min-width: 180px;"
                        onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 25px rgba(102, 126, 234, 0.3)';"
                        onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none';">
                    <i class="fas fa-paper-plane"></i> Submit Application
                </button>
            </div>
        </form>
    </div>
</div>

<style>
/* Modal responsive styles */
@media (max-width: 768px) {
    .modal-content {
        width: 98% !important;
        margin: 1% auto !important;
    }

    .form-row {
        grid-template-columns: 1fr !important;
        gap: 0 !important;
    }

    .form-row > div {
        margin-bottom: 1.5rem;
    }

    .modal-header {
        padding: 1.5rem !important;
    }

    .modal-header h2 {
        font-size: 1.5rem !important;
    }

    .modal-header p {
        font-size: 1rem !important;
    }

    .modal-content > div:last-child {
        padding: 1.5rem !important;
    }
}

@media (max-width: 480px) {
    .modal-content {
        width: 100% !important;
        margin: 0 !important;
        border-radius: 0 !important;
        height: 100vh;
        overflow-y: auto;
    }

    .modal-header {
        border-radius: 0 !important;
    }

    .close {
        top: 0.5rem !important;
        right: 0.5rem !important;
    }
}
</style>

<script>
function openApplicationModal(projectId, projectTitle) {
    document.getElementById('project-id').value = projectId;
    document.getElementById('modal-title').textContent = 'Apply for: ' + projectTitle;
    document.getElementById('application-modal').style.display = 'block';
    document.body.style.overflow = 'hidden';
}

function closeApplicationModal() {
    document.getElementById('application-modal').style.display = 'none';
    document.body.style.overflow = 'auto';
    document.getElementById('application-form').reset();
}

// Close modal when clicking outside
document.getElementById('application-modal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeApplicationModal();
    }
});

// Submit application form
function submitApplicationForm(form) {
    if (!validateForm(form)) return false;

    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;

    submitBtn.textContent = 'Submitting...';
    submitBtn.disabled = true;

    fetch('handlers/application.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('Application submitted successfully! We\'ll review it and get back to you.', 'success');
            form.reset();
            closeApplicationModal();
        } else {
            showMessage(data.message || 'An error occurred. Please try again.', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('An error occurred. Please try again.', 'error');
    })
    .finally(() => {
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    });

    return false;
}

// Validate form
function validateForm(form) {
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.style.borderColor = '#dc3545';
            isValid = false;
        } else {
            field.style.borderColor = '#ddd';
        }
    });

    // Validate email
    const emailField = form.querySelector('input[type="email"]');
    if (emailField && emailField.value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(emailField.value)) {
            emailField.style.borderColor = '#dc3545';
            isValid = false;
        }
    }

    return isValid;
}

// Show message
function showMessage(message, type) {
    // Remove existing messages
    const existingMessages = document.querySelectorAll('.alert-message');
    existingMessages.forEach(msg => msg.remove());

    // Create new message
    const messageDiv = document.createElement('div');
    messageDiv.className = `alert-message alert-${type}`;
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem 1.5rem;
        border-radius: 5px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        max-width: 400px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        ${type === 'success' ? 'background: #28a745;' : 'background: #dc3545;'}
    `;
    messageDiv.textContent = message;

    document.body.appendChild(messageDiv);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.remove();
        }
    }, 5000);
}
</script>

<?php include 'includes/footer.php'; ?>
