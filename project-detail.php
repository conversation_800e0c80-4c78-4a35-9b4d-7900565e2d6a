<?php
require_once 'config/config.php';

// Get project ID from URL
$project_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$project_id) {
    header('Location: projects.php');
    exit;
}

// Get project details from database
try {
    $database = new Database();
    $db = $database->getConnection();
    
    $stmt = $db->prepare("SELECT * FROM projects WHERE id = ? AND status = 'seeking'");
    $stmt->execute([$project_id]);
    $project = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$project) {
        header('Location: projects.php');
        exit;
    }
    
} catch(Exception $e) {
    header('Location: projects.php');
    exit;
}

$page_title = $project['title'];
$page_description = truncate_text($project['description'], 150);

include 'includes/header.php';
?>

<style>
.project-detail-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

.project-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 2rem;
    border-radius: 15px;
    margin-bottom: 0;
    text-align: center;
}

.project-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.project-status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255,255,255,0.2);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 500;
    margin-bottom: 1rem;
}

.project-meta {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
    font-size: 0.95rem;
    opacity: 0.9;
}

.project-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    margin-bottom: 3rem;
}

@media (max-width: 768px) {
    .project-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
}

.main-content {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.sidebar {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.info-card {
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.info-card h3 {
    color: #667eea;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}



.partnership-terms {
    background: #f8f9ff;
    border-left: 4px solid #667eea;
    padding: 1.5rem;
    border-radius: 0 10px 10px 0;
    margin: 2rem 0;
}

.cta-section {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 10px;
    margin: 0.5rem;
    min-width: 200px;
}

.back-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    margin-bottom: 1rem;
    transition: color 0.3s ease;
}

.back-link:hover {
    color: #5a6fd8;
}
</style>

<!-- Back to Projects -->
<div class="project-detail-container">
    <a href="projects.php" class="back-link">
        <i class="fas fa-arrow-left"></i>
        Back to All Projects
    </a>

    <!-- Project Header -->
    <div class="project-header">
        <div class="project-status-badge">
            <i class="fas fa-circle" style="color: #28a745; font-size: 0.5rem;"></i>
            Seeking Developers
        </div>
        <h1><?php echo htmlspecialchars($project['title']); ?></h1>
        <div class="project-meta">
            <span><i class="fas fa-calendar"></i> Posted <?php echo format_date($project['created_at']); ?></span>
            <span><i class="fas fa-eye"></i> Project ID: #<?php echo $project['id']; ?></span>
        </div>
    </div>

    <!-- Project Content -->
    <div class="project-content">
        <div class="main-content">
            <h2 style="color: #1a1a1a; margin-bottom: 1.5rem;">Project Description</h2>
            <div style="line-height: 1.8; color: #555; font-size: 1.1rem;">
                <?php echo nl2br(htmlspecialchars($project['description'])); ?>
            </div>

            <div class="partnership-terms">
                <h3 style="color: #667eea; margin-bottom: 1rem;">
                    <i class="fas fa-handshake"></i> Partnership Terms
                </h3>
                <p style="margin: 0; color: #666; line-height: 1.6;">
                    <?php echo nl2br(htmlspecialchars($project['partnership_terms'])); ?>
                </p>
            </div>

            <h3 style="color: #1a1a1a; margin: 2rem 0 1rem 0;">What We're Looking For</h3>
            <ul style="color: #555; line-height: 1.8; padding-left: 1.5rem;">
                <li>Experienced developers with proven track record</li>
                <li>Strong communication skills and English proficiency</li>
                <li>Ability to work in South African time zones when needed</li>
                <li>Commitment to long-term partnership success</li>
                <li>Portfolio demonstrating relevant project experience</li>
            </ul>
        </div>

        <div class="sidebar">
            <div class="info-card">
                <h3><i class="fas fa-info-circle"></i> Project Details</h3>
                <div style="color: #666; line-height: 1.6;">
                    <p><strong>Status:</strong> Actively Seeking Partners</p>
                    <p><strong>Timeline:</strong> 3-6 months typical</p>
                    <p><strong>Partnership Type:</strong> Revenue Sharing</p>
                    <p><strong>Market:</strong> South African Focus</p>
                </div>
            </div>

            <div class="cta-section">
                <h3 style="color: #1a1a1a; margin-bottom: 1rem;">Ready to Apply?</h3>
                <p style="color: #666; margin-bottom: 1.5rem;">Join this exciting project and build your success story with us.</p>
                <button class="btn btn-primary btn-large"
                        onclick="openApplicationModal(<?php echo $project['id']; ?>, '<?php echo htmlspecialchars($project['title']); ?>')">
                    <i class="fas fa-paper-plane"></i> Apply Now
                </button>
                <br>
                <a href="contact.php" class="btn btn-secondary btn-large">
                    <i class="fas fa-comments"></i> Ask Questions
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Application Modal (same as projects.php) -->
<div id="application-modal" class="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div class="modal-content" style="background: white; margin: 5% auto; padding: 2rem; width: 90%; max-width: 600px; border-radius: 10px; position: relative;">
        <span class="close" onclick="closeApplicationModal()" style="position: absolute; top: 1rem; right: 1rem; font-size: 1.5rem; cursor: pointer;">&times;</span>
        
        <h2 id="modal-title">Apply for Project</h2>
        <p style="color: #666; margin-bottom: 2rem;">Fill out the form below to apply for this partnership opportunity.</p>
        
        <form id="application-form" class="application-form" onsubmit="submitApplicationForm(this); return false;">
            <input type="hidden" id="project-id" name="project_id">
            
            <div style="margin-bottom: 1rem;">
                <label for="applicant-name" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Full Name *</label>
                <input type="text" id="applicant-name" name="name" required 
                       style="width: 100%; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px;">
            </div>
            
            <div style="margin-bottom: 1rem;">
                <label for="applicant-email" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Email Address *</label>
                <input type="email" id="applicant-email" name="email" required
                       style="width: 100%; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px;">
            </div>

            <div style="margin-bottom: 1rem;">
                <label for="applicant-country" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Country *</label>
                <select id="applicant-country" name="country" required
                        style="width: 100%; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px;">
                    <option value="">Select your country</option>
                    <option value="Pakistan">Pakistan</option>
                    <option value="India">India</option>
                    <option value="Bangladesh">Bangladesh</option>
                    <option value="Sri Lanka">Sri Lanka</option>
                    <option value="Nepal">Nepal</option>
                    <option value="Other">Other</option>
                </select>
            </div>

            <div style="margin-bottom: 1rem;">
                <label for="experience-level" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Experience Level *</label>
                <select id="experience-level" name="experience_level" required
                        style="width: 100%; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px;">
                    <option value="">Select experience level</option>
                    <option value="junior">Junior (1-2 years)</option>
                    <option value="mid">Mid-level (3-5 years)</option>
                    <option value="senior">Senior (5+ years)</option>
                    <option value="expert">Expert (10+ years)</option>
                </select>
            </div>

            <div style="margin-bottom: 1rem;">
                <label for="team-overview" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Team/Organization Overview *</label>
                <textarea id="team-overview" name="team_overview" required rows="3"
                          placeholder="Brief description of your development team/company..."
                          style="width: 100%; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px; resize: vertical;"></textarea>
            </div>

            <div style="margin-bottom: 1rem;">
                <label for="project-experience" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Previous Project Experience *</label>
                <textarea id="project-experience" name="project_experience" required rows="3"
                          placeholder="Examples of similar projects you've completed..."
                          style="width: 100%; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px; resize: vertical;"></textarea>
            </div>

            <div style="margin-bottom: 1rem;">
                <label for="development-approach" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Development Approach *</label>
                <textarea id="development-approach" name="development_approach" required rows="3"
                          placeholder="How you typically handle projects from start to finish..."
                          style="width: 100%; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px; resize: vertical;"></textarea>
            </div>

            <div style="margin-bottom: 1rem;">
                <label for="team-capabilities" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Team Size & Capabilities *</label>
                <textarea id="team-capabilities" name="team_capabilities" required rows="3"
                          placeholder="Size of your team and what you can deliver..."
                          style="width: 100%; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px; resize: vertical;"></textarea>
            </div>

            <div style="margin-bottom: 1rem;">
                <label for="portfolio-url" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Portfolio URL</label>
                <input type="url" id="portfolio-url" name="portfolio_url"
                       placeholder="https://your-portfolio.com"
                       style="width: 100%; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px;">
            </div>

            <div style="margin-bottom: 1rem;">
                <label for="application-message" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Why are you interested in this project? *</label>
                <textarea id="application-message" name="message" required rows="4"
                          placeholder="Tell us why you're the right fit for this project..."
                          style="width: 100%; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px; resize: vertical;"></textarea>
            </div>

            <div style="margin-bottom: 2rem;">
                <label for="cv-file" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Upload CV/Resume</label>
                <input type="file" id="cv-file" name="cv_file" accept=".pdf,.doc,.docx"
                       style="width: 100%; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px;">
                <small style="color: #666;">Accepted formats: PDF, DOC, DOCX (Max 5MB)</small>
            </div>

            <div style="text-align: right;">
                <button type="button" onclick="closeApplicationModal()" class="btn btn-secondary" style="margin-right: 1rem;">Cancel</button>
                <button type="submit" class="btn btn-primary">Submit Application</button>
            </div>
        </form>
    </div>
</div>

<script>
function openApplicationModal(projectId, projectTitle) {
    document.getElementById('project-id').value = projectId;
    document.getElementById('modal-title').textContent = 'Apply for: ' + projectTitle;
    document.getElementById('application-modal').style.display = 'block';
    document.body.style.overflow = 'hidden';
}

function closeApplicationModal() {
    document.getElementById('application-modal').style.display = 'none';
    document.body.style.overflow = 'auto';
    document.getElementById('application-form').reset();
}

// Close modal when clicking outside
document.getElementById('application-modal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeApplicationModal();
    }
});

// Submit application form
function submitApplicationForm(form) {
    if (!validateForm(form)) return false;

    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;

    submitBtn.textContent = 'Submitting...';
    submitBtn.disabled = true;

    fetch('handlers/application.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('Application submitted successfully! We\'ll review it and get back to you.', 'success');
            form.reset();
            closeApplicationModal();
        } else {
            showMessage(data.message || 'An error occurred. Please try again.', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('An error occurred. Please try again.', 'error');
    })
    .finally(() => {
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    });

    return false;
}

// Validate form
function validateForm(form) {
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.style.borderColor = '#dc3545';
            isValid = false;
        } else {
            field.style.borderColor = '#ddd';
        }
    });

    // Validate email
    const emailField = form.querySelector('input[type="email"]');
    if (emailField && emailField.value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(emailField.value)) {
            emailField.style.borderColor = '#dc3545';
            isValid = false;
        }
    }

    return isValid;
}

// Show message
function showMessage(message, type) {
    // Remove existing messages
    const existingMessages = document.querySelectorAll('.alert-message');
    existingMessages.forEach(msg => msg.remove());

    // Create new message
    const messageDiv = document.createElement('div');
    messageDiv.className = `alert-message alert-${type}`;
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem 1.5rem;
        border-radius: 5px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        max-width: 400px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        ${type === 'success' ? 'background: #28a745;' : 'background: #dc3545;'}
    `;
    messageDiv.textContent = message;

    document.body.appendChild(messageDiv);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.remove();
        }
    }, 5000);
}
</script>

<?php include 'includes/footer.php'; ?>
