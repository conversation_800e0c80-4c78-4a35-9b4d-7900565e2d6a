<?php
/**
 * Database Migration Script: Remove Skills Requirements
 * This script safely removes all skills-related fields from the database
 * Run this script once to update your existing database structure
 */

require_once '../config/config.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Database Migration - Remove Skills</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        h1 { color: #333; }
        .step { margin: 20px 0; padding: 15px; border-left: 4px solid #667eea; background: #f8f9ff; }
    </style>
</head>
<body>
    <h1>🔄 Database Migration: Remove Skills Requirements</h1>
    <p>This migration will remove all skills-related fields from your database as requested.</p>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<div class='step'><strong>Step 1:</strong> Checking current database structure...</div>";
    
    // Check if skills_required column exists in projects table
    $stmt = $db->prepare("SHOW COLUMNS FROM projects LIKE 'skills_required'");
    $stmt->execute();
    $skillsColumnExists = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($skillsColumnExists) {
        echo "<div class='info'>✓ Found 'skills_required' column in projects table</div>";
        
        echo "<div class='step'><strong>Step 2:</strong> Backing up existing data...</div>";
        
        // Create a backup of projects with skills data (optional - for safety)
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM projects WHERE skills_required IS NOT NULL AND skills_required != ''");
        $stmt->execute();
        $projectsWithSkills = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        echo "<div class='info'>Found {$projectsWithSkills} projects with skills data</div>";
        
        echo "<div class='step'><strong>Step 3:</strong> Removing skills_required column...</div>";
        
        // Remove the skills_required column
        $stmt = $db->prepare("ALTER TABLE projects DROP COLUMN skills_required");
        $stmt->execute();
        
        echo "<div class='success'>✓ Successfully removed 'skills_required' column from projects table</div>";
        
    } else {
        echo "<div class='info'>ℹ️ 'skills_required' column not found in projects table (already removed or never existed)</div>";
    }
    
    echo "<div class='step'><strong>Step 4:</strong> Checking developers table for skills fields...</div>";

    // Check if skills column exists in developers table
    $stmt = $db->prepare("SHOW COLUMNS FROM developers LIKE 'skills'");
    $stmt->execute();
    $devSkillsExists = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($devSkillsExists) {
        echo "<div class='info'>✓ Found 'skills' column in developers table</div>";

        // Count developers with skills data
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM developers WHERE skills IS NOT NULL AND skills != ''");
        $stmt->execute();
        $devsWithSkills = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

        echo "<div class='info'>Found {$devsWithSkills} developers with skills data</div>";

        // Remove the skills column from developers
        $stmt = $db->prepare("ALTER TABLE developers DROP COLUMN skills");
        $stmt->execute();

        echo "<div class='success'>✓ Successfully removed 'skills' column from developers table</div>";

    } else {
        echo "<div class='info'>ℹ️ 'skills' column not found in developers table</div>";
    }
    
    echo "<div class='step'><strong>Step 5:</strong> Adding new application fields...</div>";

    // Add new fields to project_applications table to replace skills
    $newFields = [
        'team_overview' => "ADD COLUMN team_overview TEXT AFTER message",
        'project_experience' => "ADD COLUMN project_experience TEXT AFTER team_overview",
        'development_approach' => "ADD COLUMN development_approach TEXT AFTER project_experience",
        'team_capabilities' => "ADD COLUMN team_capabilities TEXT AFTER development_approach"
    ];

    foreach ($newFields as $fieldName => $alterQuery) {
        // Check if field already exists
        $stmt = $db->prepare("SHOW COLUMNS FROM project_applications LIKE ?");
        $stmt->execute([$fieldName]);
        $fieldExists = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$fieldExists) {
            $stmt = $db->prepare("ALTER TABLE project_applications " . $alterQuery);
            $stmt->execute();
            echo "<div class='success'>✓ Added '{$fieldName}' column to project_applications table</div>";
        } else {
            echo "<div class='info'>ℹ️ '{$fieldName}' column already exists in project_applications table</div>";
        }
    }
    
    echo "<div class='step'><strong>Step 6:</strong> Verifying migration...</div>";
    
    // Verify the changes
    $stmt = $db->prepare("DESCRIBE projects");
    $stmt->execute();
    $projectColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $stmt = $db->prepare("DESCRIBE project_applications");
    $stmt->execute();
    $applicationColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $hasSkillsInProjects = false;
    $hasSkillsInApplications = false;
    $hasNewFields = 0;
    
    foreach ($projectColumns as $column) {
        if ($column['Field'] === 'skills_required') {
            $hasSkillsInProjects = true;
        }
    }
    
    foreach ($applicationColumns as $column) {
        if ($column['Field'] === 'skills') {
            $hasSkillsInApplications = true;
        }
        if (in_array($column['Field'], ['team_overview', 'project_experience', 'development_approach', 'team_capabilities'])) {
            $hasNewFields++;
        }
    }
    
    if (!$hasSkillsInProjects && !$hasSkillsInApplications && $hasNewFields === 4) {
        echo "<div class='success'>🎉 Migration completed successfully!</div>";
        echo "<div class='info'>
            <strong>Summary of changes:</strong><br>
            ✓ Removed 'skills_required' from projects table<br>
            ✓ Removed 'skills' from developers table<br>
            ✓ Added 4 new fields to project_applications table:<br>
            &nbsp;&nbsp;- team_overview<br>
            &nbsp;&nbsp;- project_experience<br>
            &nbsp;&nbsp;- development_approach<br>
            &nbsp;&nbsp;- team_capabilities
        </div>";
    } else {
        echo "<div class='warning'>⚠️ Migration may not have completed fully. Please check manually.</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Migration failed: " . $e->getMessage() . "</div>";
    echo "<div class='warning'>Please check your database connection and try again.</div>";
}

echo "<div style='margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 5px;'>
    <strong>Next Steps:</strong><br>
    1. Test your website to ensure everything works correctly<br>
    2. Update your application forms to use the new fields<br>
    3. If everything works, you can delete this migration file<br>
    4. Consider backing up your database before making further changes
</div>";

echo "</body></html>";
?>
