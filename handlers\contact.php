<?php
// Determine the correct path to config based on where this file is called from
$config_path = file_exists('../config/config.php') ? '../config/config.php' : 'config/config.php';
require_once $config_path;

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Sanitize and validate input
    $name = sanitize_input($_POST['name'] ?? '');
    $email = sanitize_input($_POST['email'] ?? '');
    $country = sanitize_input($_POST['country'] ?? '');
    $subject = sanitize_input($_POST['subject'] ?? '');
    $message = sanitize_input($_POST['message'] ?? '');
    $experience_level = sanitize_input($_POST['experience_level'] ?? '');
    $skills = sanitize_input($_POST['skills'] ?? '');
    $portfolio_url = sanitize_input($_POST['portfolio_url'] ?? '');
    $newsletter = isset($_POST['newsletter']) ? 1 : 0;
    
    // Validation
    $errors = [];
    
    if (empty($name)) {
        $errors[] = 'Name is required';
    }
    
    if (empty($email)) {
        $errors[] = 'Email is required';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Invalid email format';
    }
    
    if (empty($subject)) {
        $errors[] = 'Subject is required';
    }
    
    if (empty($message)) {
        $errors[] = 'Message is required';
    }
    
    if (!empty($portfolio_url) && !filter_var($portfolio_url, FILTER_VALIDATE_URL)) {
        $errors[] = 'Invalid portfolio URL format';
    }
    
    if (!empty($errors)) {
        echo json_encode(['success' => false, 'message' => implode(', ', $errors)]);
        exit;
    }
    
    // Determine message type based on subject
    $type = 'general';
    if (strpos(strtolower($subject), 'partnership') !== false) {
        $type = 'partnership';
    } elseif (strpos(strtolower($subject), 'project') !== false) {
        $type = 'project_inquiry';
    }
    
    // Save to database
    $database = new Database();
    $db = $database->getConnection();
    
    $query = "INSERT INTO messages (name, email, subject, message, type, created_at) VALUES (?, ?, ?, ?, ?, NOW())";
    $stmt = $db->prepare($query);
    $stmt->execute([$name, $email, $subject, $message, $type]);
    
    // If this is a developer inquiry, also save to developers table
    if (!empty($experience_level) && !empty($skills)) {
        // Check if developer already exists
        $checkQuery = "SELECT id FROM developers WHERE email = ?";
        $checkStmt = $db->prepare($checkQuery);
        $checkStmt->execute([$email]);
        
        if (!$checkStmt->fetch()) {
            // Insert new developer
            $devQuery = "INSERT INTO developers (name, email, country, experience_level, skills, portfolio_url, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())";
            $devStmt = $db->prepare($devQuery);
            $devStmt->execute([$name, $email, $country, $experience_level, $skills, $portfolio_url]);
        }
    }
    
    // Send email notification (disabled for local development)
    // $emailSent = sendNotificationEmail($name, $email, $subject, $message, $type);
    
    echo json_encode([
        'success' => true, 
        'message' => 'Thank you for your message! We will get back to you within 24 hours.'
    ]);
    
} catch (Exception $e) {
    error_log("Contact form error: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'An error occurred while sending your message. Please try again or contact us directly.'
    ]);
}

function sendNotificationEmail($name, $email, $subject, $message, $type) {
    // Basic email notification - you can enhance this later
    $to = ADMIN_EMAIL;
    $emailSubject = "New Contact Form Submission: " . $subject;
    
    $emailBody = "
    New contact form submission from South Safari website:
    
    Name: $name
    Email: $email
    Type: $type
    Subject: $subject
    
    Message:
    $message
    
    ---
    Submitted at: " . date('Y-m-d H:i:s') . "
    ";
    
    $headers = "From: " . SITE_EMAIL . "\r\n";
    $headers .= "Reply-To: $email\r\n";
    $headers .= "X-Mailer: PHP/" . phpversion();
    
    return mail($to, $emailSubject, $emailBody, $headers);
}
?>
