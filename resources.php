<?php
require_once 'config/config.php';

$page_title = 'Developer Resources';
$page_description = 'Comprehensive resources for development teams interested in partnering with South Safari, including partnership guidelines, FAQ, and application process.';

include 'includes/header.php';
?>

<!-- Page Header -->
<section class="section" style="background: #f8f9fa; padding: 2rem 0;">
    <div class="container">
        <h1 class="section-title">Developer Resources</h1>
        <p class="section-subtitle">Everything you need to know about partnering with South Safari</p>
    </div>
</section>

<!-- Quick Navigation -->
<section class="section" style="padding: 1rem 0; background: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
    <div class="container">
        <div class="quick-nav" style="display: flex; gap: 2rem; justify-content: center; flex-wrap: wrap;">
            <a href="#guidelines" style="color: #667eea; text-decoration: none; font-weight: 500;">Partnership Guidelines</a>
            <a href="#application" style="color: #667eea; text-decoration: none; font-weight: 500;">Application Process</a>
            <a href="#revenue" style="color: #667eea; text-decoration: none; font-weight: 500;">Revenue Model</a>
            <a href="#faq" style="color: #667eea; text-decoration: none; font-weight: 500;">FAQ</a>
            <a href="#support" style="color: #667eea; text-decoration: none; font-weight: 500;">Support</a>
        </div>
    </div>
</section>

<!-- Partnership Guidelines -->
<section class="section" id="guidelines">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Partnership Guidelines</h2>
            <p class="section-subtitle">What we look for in development team partners</p>
        </div>
        
        <div class="grid grid-2">
            <div class="card">
                <h3><i class="fas fa-users" style="color: #667eea; margin-right: 0.5rem;"></i>Team Capabilities</h3>
                <ul style="list-style: none; padding: 0;">
                    <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: #28a745; margin-right: 0.5rem;"></i>Complete development teams or organizations</li>
                    <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: #28a745; margin-right: 0.5rem;"></i>Proven track record of delivered projects</li>
                    <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: #28a745; margin-right: 0.5rem;"></i>Portfolio demonstrating business solutions</li>
                    <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: #28a745; margin-right: 0.5rem;"></i>Ability to handle full project lifecycle</li>
                    <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: #28a745; margin-right: 0.5rem;"></i>Experience with scalable, secure solutions</li>
                </ul>
            </div>

            <div class="card">
                <h3><i class="fas fa-handshake" style="color: #667eea; margin-right: 0.5rem;"></i>Partnership Qualities</h3>
                <ul style="list-style: none; padding: 0;">
                    <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: #28a745; margin-right: 0.5rem;"></i>Strong communication skills in English</li>
                    <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: #28a745; margin-right: 0.5rem;"></i>Reliable and professional work ethic</li>
                    <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: #28a745; margin-right: 0.5rem;"></i>Commitment to long-term partnerships</li>
                    <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: #28a745; margin-right: 0.5rem;"></i>Understanding of business requirements</li>
                    <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: #28a745; margin-right: 0.5rem;"></i>Flexibility with timezone coordination</li>
                </ul>
            </div>
        </div>

        <div class="card" style="margin-top: 2rem;">
            <h3>What We Value Most</h3>
            <div class="value-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-top: 1rem;">
                <div>
                    <h4><i class="fas fa-lightbulb" style="color: #667eea; margin-right: 0.5rem;"></i>Solution-Oriented Approach</h4>
                    <p>Teams that understand business needs and propose the most efficient technical solutions without being told which technologies to use.</p>
                </div>
                <div>
                    <h4><i class="fas fa-shield-alt" style="color: #667eea; margin-right: 0.5rem;"></i>Security & Best Practices</h4>
                    <p>Development teams that prioritize security, performance, and maintainability in their solutions.</p>
                </div>
                <div>
                    <h4><i class="fas fa-rocket" style="color: #667eea; margin-right: 0.5rem;"></i>Business Growth Focus</h4>
                    <p>Partners who understand that the goal is building platforms that help businesses thrive and scale.</p>
                </div>
                <div>
                    <h4><i class="fas fa-cogs" style="color: #667eea; margin-right: 0.5rem;"></i>Complete Solutions</h4>
                    <p>Teams capable of delivering working products from concept to deployment, not just individual components.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Application Process -->
<section class="section" style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); position: relative; overflow: hidden;" id="application">
    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; opacity: 0.1; background-image: radial-gradient(circle at 25% 25%, #667eea 2px, transparent 2px), radial-gradient(circle at 75% 75%, #764ba2 1px, transparent 1px); background-size: 50px 50px; pointer-events: none;"></div>

    <div class="container" style="position: relative; z-index: 1;">
        <div class="section-header">
            <h2 class="section-title" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">Application Process</h2>
            <p class="section-subtitle" style="font-size: 1.3rem; color: #4a5568;">Simple steps to become a South Safari partner</p>
        </div>

        <!-- Process Flow Container -->
        <div class="process-flow-container" style="max-width: 1000px; margin: 0 auto; position: relative;">

            <!-- Desktop Flow (Horizontal) -->
            <div class="desktop-flow" style="display: none;">
                <div class="flow-line" style="position: absolute; top: 50%; left: 10%; right: 10%; height: 4px; background: linear-gradient(90deg, #667eea 0%, #764ba2 100%); border-radius: 2px; z-index: 1;"></div>

                <div class="process-steps-horizontal" style="display: flex; justify-content: space-between; align-items: center; position: relative; z-index: 2;">
                    <div class="step-item" style="flex: 1; text-align: center; position: relative;">
                        <div class="step-circle" style="width: 80px; height: 80px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 1.5rem; margin: 0 auto 1rem; box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3); border: 4px solid white;">1</div>
                        <div class="step-content" style="background: white; padding: 1.5rem; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); border: 1px solid rgba(102, 126, 234, 0.1); max-width: 180px; margin: 0 auto;">
                            <h4 style="color: #2d3748; margin-bottom: 0.5rem; font-size: 1rem;">Submit Application</h4>
                            <p style="font-size: 0.85rem; color: #718096; margin: 0; line-height: 1.4;">Fill out our application form</p>
                        </div>
                    </div>

                    <div class="step-item" style="flex: 1; text-align: center; position: relative;">
                        <div class="step-circle" style="width: 80px; height: 80px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 1.5rem; margin: 0 auto 1rem; box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3); border: 4px solid white;">2</div>
                        <div class="step-content" style="background: white; padding: 1.5rem; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); border: 1px solid rgba(102, 126, 234, 0.1); max-width: 180px; margin: 0 auto;">
                            <h4 style="color: #2d3748; margin-bottom: 0.5rem; font-size: 1rem;">Initial Review</h4>
                            <p style="font-size: 0.85rem; color: #718096; margin: 0; line-height: 1.4;">Review within 48 hours</p>
                        </div>
                    </div>

                    <div class="step-item" style="flex: 1; text-align: center; position: relative;">
                        <div class="step-circle" style="width: 80px; height: 80px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 1.5rem; margin: 0 auto 1rem; box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3); border: 4px solid white;">3</div>
                        <div class="step-content" style="background: white; padding: 1.5rem; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); border: 1px solid rgba(102, 126, 234, 0.1); max-width: 180px; margin: 0 auto;">
                            <h4 style="color: #2d3748; margin-bottom: 0.5rem; font-size: 1rem;">Business Discussion</h4>
                            <p style="font-size: 0.85rem; color: #718096; margin: 0; line-height: 1.4;">Video call assessment</p>
                        </div>
                    </div>

                    <div class="step-item" style="flex: 1; text-align: center; position: relative;">
                        <div class="step-circle" style="width: 80px; height: 80px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 1.5rem; margin: 0 auto 1rem; box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3); border: 4px solid white;">4</div>
                        <div class="step-content" style="background: white; padding: 1.5rem; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); border: 1px solid rgba(102, 126, 234, 0.1); max-width: 180px; margin: 0 auto;">
                            <h4 style="color: #2d3748; margin-bottom: 0.5rem; font-size: 1rem;">Project Matching</h4>
                            <p style="font-size: 0.85rem; color: #718096; margin: 0; line-height: 1.4;">Find suitable projects</p>
                        </div>
                    </div>

                    <div class="step-item" style="flex: 1; text-align: center; position: relative;">
                        <div class="step-circle" style="width: 80px; height: 80px; background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 1.5rem; margin: 0 auto 1rem; box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3); border: 4px solid white;">5</div>
                        <div class="step-content" style="background: white; padding: 1.5rem; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); border: 1px solid rgba(40, 167, 69, 0.1); max-width: 180px; margin: 0 auto;">
                            <h4 style="color: #2d3748; margin-bottom: 0.5rem; font-size: 1rem;">Partnership Agreement</h4>
                            <p style="font-size: 0.85rem; color: #718096; margin: 0; line-height: 1.4;">Start your first project</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mobile Flow (Vertical) -->
            <div class="mobile-flow" style="display: block;">
                <div class="process-steps-vertical" style="position: relative;">
                    <!-- Vertical connecting line -->
                    <div class="vertical-line" style="position: absolute; left: 40px; top: 80px; bottom: 80px; width: 4px; background: linear-gradient(180deg, #667eea 0%, #764ba2 80%, #28a745 100%); border-radius: 2px; z-index: 1;"></div>

                    <div class="step-item-vertical" style="display: flex; align-items: center; margin-bottom: 3rem; position: relative; z-index: 2;">
                        <div class="step-circle" style="width: 80px; height: 80px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 1.5rem; box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3); border: 4px solid white; flex-shrink: 0;">1</div>
                        <div class="step-content" style="background: white; padding: 2rem; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); border: 1px solid rgba(102, 126, 234, 0.1); margin-left: 2rem; flex: 1;">
                            <h4 style="color: #2d3748; margin-bottom: 1rem; font-size: 1.2rem;">Submit Application</h4>
                            <p style="color: #718096; margin: 0; line-height: 1.6;">Fill out our application form with your team overview, project experience, and development approach.</p>
                        </div>
                    </div>

                    <div class="step-item-vertical" style="display: flex; align-items: center; margin-bottom: 3rem; position: relative; z-index: 2;">
                        <div class="step-circle" style="width: 80px; height: 80px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 1.5rem; box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3); border: 4px solid white; flex-shrink: 0;">2</div>
                        <div class="step-content" style="background: white; padding: 2rem; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); border: 1px solid rgba(102, 126, 234, 0.1); margin-left: 2rem; flex: 1;">
                            <h4 style="color: #2d3748; margin-bottom: 1rem; font-size: 1.2rem;">Initial Review</h4>
                            <p style="color: #718096; margin: 0; line-height: 1.6;">Our team reviews your team capabilities and portfolio within 48 hours.</p>
                        </div>
                    </div>

                    <div class="step-item-vertical" style="display: flex; align-items: center; margin-bottom: 3rem; position: relative; z-index: 2;">
                        <div class="step-circle" style="width: 80px; height: 80px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 1.5rem; box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3); border: 4px solid white; flex-shrink: 0;">3</div>
                        <div class="step-content" style="background: white; padding: 2rem; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); border: 1px solid rgba(102, 126, 234, 0.1); margin-left: 2rem; flex: 1;">
                            <h4 style="color: #2d3748; margin-bottom: 1rem; font-size: 1.2rem;">Business Discussion</h4>
                            <p style="color: #718096; margin: 0; line-height: 1.6;">Video call to discuss your approach and assess business solution capabilities.</p>
                        </div>
                    </div>

                    <div class="step-item-vertical" style="display: flex; align-items: center; margin-bottom: 3rem; position: relative; z-index: 2;">
                        <div class="step-circle" style="width: 80px; height: 80px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 1.5rem; box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3); border: 4px solid white; flex-shrink: 0;">4</div>
                        <div class="step-content" style="background: white; padding: 2rem; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); border: 1px solid rgba(102, 126, 234, 0.1); margin-left: 2rem; flex: 1;">
                            <h4 style="color: #2d3748; margin-bottom: 1rem; font-size: 1.2rem;">Project Matching</h4>
                            <p style="color: #718096; margin: 0; line-height: 1.6;">We match you with suitable projects based on your team capabilities and business focus.</p>
                        </div>
                    </div>

                    <div class="step-item-vertical" style="display: flex; align-items: center; margin-bottom: 2rem; position: relative; z-index: 2;">
                        <div class="step-circle" style="width: 80px; height: 80px; background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 1.5rem; box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3); border: 4px solid white; flex-shrink: 0;">5</div>
                        <div class="step-content" style="background: white; padding: 2rem; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); border: 1px solid rgba(40, 167, 69, 0.1); margin-left: 2rem; flex: 1;">
                            <h4 style="color: #2d3748; margin-bottom: 1rem; font-size: 1.2rem;">Partnership Agreement</h4>
                            <p style="color: #718096; margin: 0; line-height: 1.6;">Sign partnership agreement and begin working on your first project.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Call to Action -->
        <div class="text-center" style="margin-top: 4rem;">
            <div style="background: white; padding: 3rem; border-radius: 20px; box-shadow: 0 15px 40px rgba(0,0,0,0.1); border: 1px solid rgba(102, 126, 234, 0.1); max-width: 600px; margin: 0 auto;">
                <h3 style="color: #2d3748; margin-bottom: 1rem; font-size: 1.5rem;">Ready to Start Your Journey?</h3>
                <p style="color: #718096; margin-bottom: 2rem; font-size: 1.1rem;">Join our network of successful development partners and start building amazing projects together.</p>
                <a href="contact.php" class="btn btn-primary" style="padding: 1.25rem 3rem; font-size: 1.1rem; font-weight: 600; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; border-radius: 50px; box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3); transition: all 0.3s ease;">
                    <i class="fas fa-rocket" style="margin-right: 0.5rem;"></i>Start Your Application
                </a>
            </div>
        </div>
    </div>
</section>

<style>
/* Application Process Flow Responsive Styles */
@media (min-width: 1024px) {
    .desktop-flow {
        display: block !important;
    }

    .mobile-flow {
        display: none !important;
    }
}

@media (max-width: 1023px) {
    .desktop-flow {
        display: none !important;
    }

    .mobile-flow {
        display: block !important;
    }
}

@media (max-width: 768px) {
    .step-item-vertical {
        flex-direction: column;
        text-align: center;
    }

    .step-item-vertical .step-content {
        margin-left: 0 !important;
        margin-top: 1.5rem;
        width: 100%;
    }

    .vertical-line {
        display: none !important;
    }

    .step-item-vertical {
        margin-bottom: 2rem !important;
    }
}

@media (max-width: 480px) {
    .step-circle {
        width: 60px !important;
        height: 60px !important;
        font-size: 1.2rem !important;
    }

    .step-content {
        padding: 1.5rem !important;
    }

    .step-content h4 {
        font-size: 1.1rem !important;
    }

    .step-content p {
        font-size: 0.9rem !important;
    }
}

/* Hover effects for interactive elements */
.step-item:hover .step-circle,
.step-item-vertical:hover .step-circle {
    transform: scale(1.1);
    transition: transform 0.3s ease;
}

.step-item:hover .step-content,
.step-item-vertical:hover .step-content {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15) !important;
    transition: all 0.3s ease;
}

/* Animation for the flow lines */
.flow-line::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 0;
    background: linear-gradient(90deg, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0.4) 100%);
    animation: flowAnimation 3s ease-in-out infinite;
}

.vertical-line::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 0;
    background: linear-gradient(180deg, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0.4) 100%);
    animation: verticalFlowAnimation 3s ease-in-out infinite;
}

@keyframes flowAnimation {
    0% { width: 0; }
    50% { width: 100%; }
    100% { width: 0; }
}

@keyframes verticalFlowAnimation {
    0% { height: 0; }
    50% { height: 100%; }
    100% { height: 0; }
}
</style>

<!-- Revenue Model -->
<section class="section" id="revenue">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Revenue Sharing Model</h2>
            <p class="section-subtitle">Transparent and fair partnership terms</p>
        </div>
        
        <div class="grid grid-2">
            <div class="card">
                <h3>Standard Partnership (60/40)</h3>
                <div style="margin: 1rem 0;">
                    <div style="background: #667eea; color: white; padding: 1rem; border-radius: 8px; margin-bottom: 1rem;">
                        <strong>Developer: 60%</strong>
                        <p style="margin: 0.5rem 0 0 0; opacity: 0.9;">You focus on development and technical delivery</p>
                    </div>
                    <div style="background: #28a745; color: white; padding: 1rem; border-radius: 8px;">
                        <strong>South Safari: 40%</strong>
                        <p style="margin: 0.5rem 0 0 0; opacity: 0.9;">We handle business development, client relations, and support</p>
                    </div>
                </div>
                <p><strong>Best for:</strong> Experienced developers who want to focus purely on technical work</p>
            </div>
            
            <div class="card">
                <h3>Premium Partnership (70/30)</h3>
                <div style="margin: 1rem 0;">
                    <div style="background: #667eea; color: white; padding: 1rem; border-radius: 8px; margin-bottom: 1rem;">
                        <strong>Developer: 70%</strong>
                        <p style="margin: 0.5rem 0 0 0; opacity: 0.9;">You take on additional responsibilities</p>
                    </div>
                    <div style="background: #28a745; color: white; padding: 1rem; border-radius: 8px;">
                        <strong>South Safari: 30%</strong>
                        <p style="margin: 0.5rem 0 0 0; opacity: 0.9;">We provide market access and strategic support</p>
                    </div>
                </div>
                <p><strong>Best for:</strong> Senior developers who can handle client communication and project management</p>
            </div>
        </div>
        
        <div class="card" style="margin-top: 2rem;">
            <h3>What's Included in Our Support</h3>
            <div class="grid grid-2" style="margin-top: 1rem;">
                <ul style="list-style: none; padding: 0;">
                    <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: #28a745; margin-right: 0.5rem;"></i>Client acquisition and relationship management</li>
                    <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: #28a745; margin-right: 0.5rem;"></i>Contract negotiation and legal support</li>
                    <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: #28a745; margin-right: 0.5rem;"></i>Payment processing and invoicing</li>
                    <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: #28a745; margin-right: 0.5rem;"></i>Marketing and business development</li>
                </ul>
                <ul style="list-style: none; padding: 0;">
                    <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: #28a745; margin-right: 0.5rem;"></i>Technical mentorship and guidance</li>
                    <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: #28a745; margin-right: 0.5rem;"></i>Access to South African business network</li>
                    <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: #28a745; margin-right: 0.5rem;"></i>Ongoing project support and consultation</li>
                    <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: #28a745; margin-right: 0.5rem;"></i>Professional development opportunities</li>
                </ul>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="section" style="background: #f8f9fa;" id="faq">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Frequently Asked Questions</h2>
        </div>
        
        <div style="max-width: 800px; margin: 0 auto;">
            <div class="faq-item" style="background: white; margin-bottom: 1rem; border-radius: 10px; overflow: hidden;">
                <div class="faq-question" style="padding: 1.5rem; cursor: pointer; background: white; border-bottom: 1px solid #eee;" onclick="toggleFaq(this)">
                    <h4 style="margin: 0; display: flex; justify-content: space-between; align-items: center;">
                        How long does the application process take?
                        <i class="fas fa-chevron-down"></i>
                    </h4>
                </div>
                <div class="faq-answer" style="padding: 0 1.5rem; max-height: 0; overflow: hidden; transition: all 0.3s ease;">
                    <div style="padding: 1rem 0;">
                        <p>The complete application process typically takes 1-2 weeks. Initial review happens within 48 hours, followed by a business discussion, and then project matching based on your team capabilities and availability.</p>
                    </div>
                </div>
            </div>
            
            <div class="faq-item" style="background: white; margin-bottom: 1rem; border-radius: 10px; overflow: hidden;">
                <div class="faq-question" style="padding: 1.5rem; cursor: pointer; background: white; border-bottom: 1px solid #eee;" onclick="toggleFaq(this)">
                    <h4 style="margin: 0; display: flex; justify-content: space-between; align-items: center;">
                        Do I need to be available during South African business hours?
                        <i class="fas fa-chevron-down"></i>
                    </h4>
                </div>
                <div class="faq-answer" style="padding: 0 1.5rem; max-height: 0; overflow: hidden; transition: all 0.3s ease;">
                    <div style="padding: 1rem 0;">
                        <p>Not necessarily. While some overlap with South African business hours (GMT+2) is helpful for communication, we work with developers across different timezones. We'll coordinate meeting times that work for both parties.</p>
                    </div>
                </div>
            </div>
            
            <div class="faq-item" style="background: white; margin-bottom: 1rem; border-radius: 10px; overflow: hidden;">
                <div class="faq-question" style="padding: 1.5rem; cursor: pointer; background: white; border-bottom: 1px solid #eee;" onclick="toggleFaq(this)">
                    <h4 style="margin: 0; display: flex; justify-content: space-between; align-items: center;">
                        Can I work on multiple projects simultaneously?
                        <i class="fas fa-chevron-down"></i>
                    </h4>
                </div>
                <div class="faq-answer" style="padding: 0 1.5rem; max-height: 0; overflow: hidden; transition: all 0.3s ease;">
                    <div style="padding: 1rem 0;">
                        <p>Yes, experienced partners often work on multiple projects. We'll help you manage your workload to ensure quality delivery on all projects. We prefer partners who can commit to long-term relationships rather than one-off projects.</p>
                    </div>
                </div>
            </div>
            
            <div class="faq-item" style="background: white; margin-bottom: 1rem; border-radius: 10px; overflow: hidden;">
                <div class="faq-question" style="padding: 1.5rem; cursor: pointer; background: white; border-bottom: 1px solid #eee;" onclick="toggleFaq(this)">
                    <h4 style="margin: 0; display: flex; justify-content: space-between; align-items: center;">
                        How are payments processed?
                        <i class="fas fa-chevron-down"></i>
                    </h4>
                </div>
                <div class="faq-answer" style="padding: 0 1.5rem; max-height: 0; overflow: hidden; transition: all 0.3s ease;">
                    <div style="padding: 1rem 0;">
                        <p>Payments are processed monthly via international bank transfer or PayPal. We handle all invoicing and client payments, then transfer your share according to the agreed schedule. All payments are in USD or ZAR.</p>
                    </div>
                </div>
            </div>
            
            <div class="faq-item" style="background: white; margin-bottom: 1rem; border-radius: 10px; overflow: hidden;">
                <div class="faq-question" style="padding: 1.5rem; cursor: pointer; background: white; border-bottom: 1px solid #eee;" onclick="toggleFaq(this)">
                    <h4 style="margin: 0; display: flex; justify-content: space-between; align-items: center;">
                        What if a project doesn't work out?
                        <i class="fas fa-chevron-down"></i>
                    </h4>
                </div>
                <div class="faq-answer" style="padding: 0 1.5rem; max-height: 0; overflow: hidden; transition: all 0.3s ease;">
                    <div style="padding: 1rem 0;">
                        <p>We have clear project milestones and regular check-ins to prevent issues. If a project needs to be terminated, we ensure fair compensation for work completed and help match you with a more suitable opportunity.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Support Section -->
<section class="section" id="support">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Get Support</h2>
            <p class="section-subtitle">We're here to help you succeed</p>
        </div>
        
        <div class="grid grid-3">
            <div class="card text-center">
                <i class="fab fa-whatsapp" style="font-size: 2.5rem; color: #25D366; margin-bottom: 1rem;"></i>
                <h3>WhatsApp Support</h3>
                <p>Quick questions and immediate assistance</p>
                <a href="<?php echo WHATSAPP_LINK; ?>" target="_blank" class="btn btn-primary" style="background: #25D366;">Message Us</a>
            </div>
            
            <div class="card text-center">
                <i class="fas fa-envelope" style="font-size: 2.5rem; color: #667eea; margin-bottom: 1rem;"></i>
                <h3>Email Support</h3>
                <p>Detailed inquiries and documentation</p>
                <a href="mailto:<?php echo SITE_EMAIL; ?>" class="btn btn-primary">Send Email</a>
            </div>
            
            <div class="card text-center">
                <i class="fas fa-calendar" style="font-size: 2.5rem; color: #28a745; margin-bottom: 1rem;"></i>
                <h3>Schedule Call</h3>
                <p>One-on-one consultation and guidance</p>
                <a href="contact.php" class="btn btn-primary">Book Meeting</a>
            </div>
        </div>
    </div>
</section>

<script>
function toggleFaq(element) {
    const answer = element.nextElementSibling;
    const icon = element.querySelector('i');
    
    if (answer.style.maxHeight === '0px' || answer.style.maxHeight === '') {
        answer.style.maxHeight = answer.scrollHeight + 'px';
        icon.classList.remove('fa-chevron-down');
        icon.classList.add('fa-chevron-up');
    } else {
        answer.style.maxHeight = '0px';
        icon.classList.remove('fa-chevron-up');
        icon.classList.add('fa-chevron-down');
    }
}
</script>

<?php include 'includes/footer.php'; ?>
