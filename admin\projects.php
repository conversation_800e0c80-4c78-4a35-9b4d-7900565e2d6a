<?php
session_start();
require_once '../config/config.php';

// Check if admin is logged in
if (!is_admin_logged_in()) {
    redirect('login.php');
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Handle actions
    $action = $_GET['action'] ?? 'list';
    $message = '';
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if ($action === 'add') {
            $title = sanitize_input($_POST['title']);
            $description = sanitize_input($_POST['description']);
            $partnership_terms = sanitize_input($_POST['partnership_terms']);
            $status = sanitize_input($_POST['status']);

            $stmt = $db->prepare("INSERT INTO projects (title, description, partnership_terms, status) VALUES (?, ?, ?, ?)");
            $stmt->execute([$title, $description, $partnership_terms, $status]);
            $message = "Project added successfully!";
            $action = 'list';
        } elseif ($action === 'edit') {
            $id = (int)$_POST['id'];
            $title = sanitize_input($_POST['title']);
            $description = sanitize_input($_POST['description']);
            $partnership_terms = sanitize_input($_POST['partnership_terms']);
            $status = sanitize_input($_POST['status']);

            $stmt = $db->prepare("UPDATE projects SET title = ?, description = ?, partnership_terms = ?, status = ? WHERE id = ?");
            $stmt->execute([$title, $description, $partnership_terms, $status, $id]);
            $message = "Project updated successfully!";
            $action = 'list';
        } elseif ($action === 'delete') {
            $id = (int)$_POST['id'];
            $stmt = $db->prepare("DELETE FROM projects WHERE id = ?");
            $stmt->execute([$id]);
            $message = "Project deleted successfully!";
            $action = 'list';
        }
    }
    
    // Get projects for listing
    if ($action === 'list') {
        $stmt = $db->prepare("SELECT * FROM projects ORDER BY created_at DESC");
        $stmt->execute();
        $projects = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    // Get single project for editing
    if ($action === 'edit' && isset($_GET['id'])) {
        $id = (int)$_GET['id'];
        $stmt = $db->prepare("SELECT * FROM projects WHERE id = ?");
        $stmt->execute([$id]);
        $project = $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
} catch (Exception $e) {
    $message = "Error: " . $e->getMessage();
    $projects = [];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Projects - South Safari Admin</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .admin-header {
            background: #1a1a1a;
            color: white;
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .admin-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-nav h1 {
            color: white;
            margin: 0;
            font-size: 1.5rem;
        }

        .admin-nav .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .admin-nav .nav-links a {
            color: #ccc;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .admin-nav .nav-links a:hover {
            color: white;
        }
        .admin-main {
            padding: 2rem 0;
            min-height: calc(100vh - 200px);
        }
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            cursor: pointer;
            transition: all 0.3s;
        }
        .btn-primary {
            background: #667eea;
            color: white;
        }
        .btn-primary:hover {
            background: #5a6fd8;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .projects-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .projects-table table {
            width: 100%;
            border-collapse: collapse;
        }
        .projects-table th,
        .projects-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        .projects-table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        .status-seeking {
            background: #d4edda;
            color: #155724;
        }
        .status-in-progress {
            background: #fff3cd;
            color: #856404;
        }
        .status-completed {
            background: #d1ecf1;
            color: #0c5460;
        }
        .form-group {
            margin-bottom: 1.5rem;
        }
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        .form-group textarea {
            height: 120px;
            resize: vertical;
        }
        .message {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <header class="admin-header">
        <div class="container">
            <nav class="admin-nav">
                <h1><i class="fas fa-mountain"></i> South Safari Admin</h1>
                <div class="nav-links">
                    <a href="dashboard.php">Dashboard</a>
                    <a href="projects.php">Projects</a>
                    <a href="developers.php">Developers</a>
                    <a href="messages.php">Messages</a>
                    <a href="../index.php" target="_blank">View Site</a>
                    <a href="logout.php">Logout</a>
                </div>
            </nav>
        </div>
    </header>

    <main class="admin-main">
        <div class="container">
            <?php if ($message): ?>
                <div class="message <?php echo strpos($message, 'Error') === 0 ? 'error' : 'success'; ?>">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <?php if ($action === 'list'): ?>
                <div class="page-header">
                    <h2>Manage Projects</h2>
                    <a href="?action=add" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add New Project
                    </a>
                </div>

                <div class="projects-table">
                    <table>
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($projects as $project): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($project['title']); ?></strong>
                                        <br>
                                        <small style="color: #666;">
                                            <?php echo htmlspecialchars(truncate_text($project['description'], 80)); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <span class="status-badge status-<?php echo $project['status']; ?>">
                                            <?php
                                            $status_display = $project['status'];
                                            if ($status_display === 'in-progress') {
                                                echo 'In Progress';
                                            } elseif ($status_display === 'seeking') {
                                                echo 'Seeking Partners';
                                            } else {
                                                echo ucfirst($status_display);
                                            }
                                            ?>
                                        </span>
                                    </td>
                                    <td><?php echo format_date($project['created_at']); ?></td>
                                    <td>
                                        <a href="?action=edit&id=<?php echo $project['id']; ?>" class="btn btn-primary" style="padding: 0.5rem; margin-right: 0.5rem;">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this project?');">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="id" value="<?php echo $project['id']; ?>">
                                            <button type="submit" class="btn btn-danger" style="padding: 0.5rem;">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

            <?php elseif ($action === 'add' || $action === 'edit'): ?>
                <div class="page-header">
                    <h2><?php echo $action === 'add' ? 'Add New Project' : 'Edit Project'; ?></h2>
                    <a href="projects.php" class="btn btn-primary">
                        <i class="fas fa-arrow-left"></i> Back to Projects
                    </a>
                </div>

                <div style="background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                    <form method="POST">
                        <input type="hidden" name="action" value="<?php echo $action; ?>">
                        <?php if ($action === 'edit'): ?>
                            <input type="hidden" name="id" value="<?php echo $project['id']; ?>">
                        <?php endif; ?>

                        <div class="form-group">
                            <label for="title">Project Title</label>
                            <input type="text" id="title" name="title" required 
                                   value="<?php echo htmlspecialchars($project['title'] ?? ''); ?>">
                        </div>

                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea id="description" name="description" required><?php echo htmlspecialchars($project['description'] ?? ''); ?></textarea>
                        </div>



                        <div class="form-group">
                            <label for="partnership_terms">Partnership Terms</label>
                            <textarea id="partnership_terms" name="partnership_terms" required><?php echo htmlspecialchars($project['partnership_terms'] ?? ''); ?></textarea>
                        </div>

                        <div class="form-group">
                            <label for="status">Status</label>
                            <select id="status" name="status" required>
                                <option value="seeking" <?php echo ($project['status'] ?? '') === 'seeking' ? 'selected' : ''; ?>>Seeking Partners</option>
                                <option value="in-progress" <?php echo ($project['status'] ?? '') === 'in-progress' ? 'selected' : ''; ?>>In Progress</option>
                                <option value="completed" <?php echo ($project['status'] ?? '') === 'completed' ? 'selected' : ''; ?>>Completed</option>
                            </select>
                        </div>

                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> <?php echo $action === 'add' ? 'Add Project' : 'Update Project'; ?>
                        </button>
                    </form>
                </div>
            <?php endif; ?>
        </div>
    </main>
</body>
</html>
