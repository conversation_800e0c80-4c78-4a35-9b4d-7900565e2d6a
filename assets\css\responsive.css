/* Responsive Styles for South Safari */

/* Tablet Styles */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    /* Navigation */
    .nav-menu {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: white;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        transition: left 0.3s ease;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-list {
        flex-direction: column;
        gap: 1rem;
        padding: 2rem 0;
        width: 100%;
        text-align: center;
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .nav-toggle.active span:nth-child(1) {
        transform: rotate(-45deg) translate(-5px, 6px);
    }
    
    .nav-toggle.active span:nth-child(2) {
        opacity: 0;
    }
    
    .nav-toggle.active span:nth-child(3) {
        transform: rotate(45deg) translate(-5px, -6px);
    }
    
    /* Hero Section */
    .hero h1 {
        font-size: 2.5rem;
    }
    
    .hero p {
        font-size: 1.1rem;
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 300px;
    }
    
    /* Typography */
    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.5rem; }
    
    .section-title {
        font-size: 2rem;
    }
    
    /* Grid Layouts */
    .grid-2,
    .grid-3,
    .grid-4 {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
    
    /* Cards */
    .card {
        padding: 1.5rem;
    }
    
    /* Enhanced Footer Responsive */
    .newsletter-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .footer-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-brand-column {
        padding-right: 0;
        text-align: center;
    }

    .footer-stats {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.75rem;
    }

    .social-links-enhanced {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
    }

    .footer-bottom-content-enhanced {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        text-align: center;
    }

    .footer-bottom-left,
    .footer-bottom-center,
    .footer-bottom-right {
        justify-content: center;
    }

    .footer-badges {
        justify-content: center;
        flex-wrap: wrap;
    }

    .footer-bottom-links-enhanced {
        justify-content: center;
        flex-wrap: wrap;
    }
}

/* Mobile Styles */
@media (max-width: 480px) {
    .container {
        padding: 0 10px;
    }
    
    /* Header */
    .navbar {
        padding: 0.75rem 0;
    }
    
    .logo {
        font-size: 1.25rem;
    }
    
    .main-content {
        margin-top: 70px;
    }
    
    /* Hero Section */
    .hero {
        padding: 3rem 0;
    }
    
    .hero h1 {
        font-size: 2rem;
        margin-bottom: 1rem;
    }
    
    .hero p {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }
    
    /* Sections */
    .section {
        padding: 3rem 0;
    }
    
    .section-header {
        margin-bottom: 2rem;
    }
    
    /* Typography */
    h1 { font-size: 1.75rem; }
    h2 { font-size: 1.5rem; }
    h3 { font-size: 1.25rem; }
    
    .section-title {
        font-size: 1.75rem;
    }
    
    .section-subtitle {
        font-size: 1rem;
    }
    
    /* Cards */
    .card {
        padding: 1rem;
    }
    
    .card-title {
        font-size: 1.1rem;
    }
    
    /* Stats */
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .stat-item h3 {
        font-size: 2rem;
    }
    
    /* Buttons */
    .btn {
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
    }
    
    /* Enhanced Footer Mobile */
    .footer-newsletter {
        padding: 2rem 0;
    }

    .newsletter-content {
        gap: 1.5rem;
    }

    .newsletter-text h3 {
        font-size: 1.4rem;
    }

    .newsletter-text p {
        font-size: 1rem;
    }

    .input-group {
        flex-direction: column;
        border-radius: 10px;
    }

    .input-group input {
        border-radius: 10px 10px 0 0;
    }

    .btn-newsletter {
        border-radius: 0 0 10px 10px;
        padding: 1rem;
    }

    .footer-main {
        padding: 2rem 0 1rem;
    }

    .footer-grid {
        gap: 2rem;
    }

    .logo-enhanced {
        justify-content: center;
        margin-bottom: 1rem;
    }

    .logo-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .logo-name {
        font-size: 1.3rem;
    }

    .footer-stats {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .stat-item {
        padding: 0.75rem;
    }

    .stat-number {
        font-size: 1.3rem;
    }

    .footer-title {
        font-size: 1.1rem;
        justify-content: center;
    }

    .contact-item {
        padding: 0.75rem;
        margin-bottom: 1rem;
    }

    .contact-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .social-links-enhanced {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .social-link {
        padding: 1rem;
        justify-content: center;
    }

    .footer-bottom-enhanced {
        padding: 1.5rem 0;
    }

    .footer-badges {
        gap: 0.5rem;
    }

    .badge {
        padding: 0.4rem 0.8rem;
        font-size: 0.75rem;
    }

    .footer-bottom-links-enhanced {
        gap: 1rem;
    }

    .footer-bottom-links-enhanced a {
        font-size: 0.8rem;
    }
}

/* Large Desktop Styles */
@media (min-width: 1400px) {
    .container {
        max-width: 1400px;
    }
    
    .hero h1 {
        font-size: 3.5rem;
    }
    
    .hero p {
        font-size: 1.3rem;
    }
    
    .section-title {
        font-size: 3rem;
    }
    
    .section-subtitle {
        font-size: 1.3rem;
    }
}

/* Print Styles */
@media print {
    .header,
    .footer,
    .nav-toggle,
    .whatsapp-btn,
    .btn {
        display: none;
    }
    
    .main-content {
        margin-top: 0;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
        page-break-inside: avoid;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .card {
        border: 2px solid #333;
    }
    
    .btn-secondary {
        border-width: 3px;
    }
    
    .nav-link.active::after {
        height: 3px;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .card:hover {
        transform: none;
    }
    
    .btn:hover {
        transform: none;
    }
}
