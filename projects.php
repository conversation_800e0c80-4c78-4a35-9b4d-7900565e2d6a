<?php
require_once 'config/config.php';

$page_title = 'Current Projects';
$page_description = 'Discover active project opportunities seeking skilled developers for partnerships in the South African market.';

// Get projects from database
try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Handle search and filter
    $search = isset($_GET['search']) ? sanitize_input($_GET['search']) : '';
    $filter = isset($_GET['filter']) ? sanitize_input($_GET['filter']) : 'all';
    
    // Build query
    $query = "SELECT * FROM projects WHERE status = 'seeking'";
    $params = [];
    
    if (!empty($search)) {
        $query .= " AND (title LIKE ? OR description LIKE ?)";
        $searchTerm = "%$search%";
        $params = array_merge($params, [$searchTerm, $searchTerm]);
    }
    
    $query .= " ORDER BY created_at DESC";
    
    $stmt = $db->prepare($query);
    $stmt->execute($params);
    $projects = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch(Exception $e) {
    $projects = [];
}

include 'includes/header.php';
?>

<style>
/* Improved Projects Page Styles */
.projects-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    margin-top: 0;
}

@media (max-width: 1024px) {
    .projects-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .projects-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

.project-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.project-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.12);
}

.project-card .card-header {
    padding: 1.25rem 1.25rem 0.75rem 1.25rem;
    border-bottom: 1px solid #f0f0f0;
}

.project-card .card-title {
    font-size: 1.2rem;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 0.5rem;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.project-card .card-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.85rem;
    color: #666;
}

.project-status {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    background: #e8f5e8;
    color: #2d5a2d;
    padding: 0.3rem 0.6rem;
    border-radius: 15px;
    font-weight: 500;
    font-size: 0.8rem;
}

.project-card .card-content {
    padding: 1rem 1.25rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.project-card .card-content p {
    color: #555;
    line-height: 1.5;
    margin-bottom: 1rem;
    font-size: 0.95rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}



.partnership-terms {
    background: #f8f9ff;
    border-left: 3px solid #667eea;
    padding: 0.75rem;
    border-radius: 0 6px 6px 0;
    margin-top: auto;
}

.partnership-terms strong {
    color: #667eea;
    font-weight: 600;
    font-size: 0.85rem;
}

.partnership-terms p {
    margin: 0.4rem 0 0 0;
    color: #666;
    font-size: 0.85rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.project-card .card-footer {
    padding: 1rem 1.25rem;
    background: #fafafa;
    display: flex;
    gap: 0.75rem;
    border-top: 1px solid #f0f0f0;
}

.project-card .btn {
    padding: 0.6rem 1rem;
    border-radius: 6px;
    font-weight: 600;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.4rem;
    flex: 1;
    text-align: center;
}

.project-card .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.project-card .btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.project-card .btn-secondary {
    background: white;
    color: #667eea;
    border: 1px solid #667eea;
}

.project-card .btn-secondary:hover {
    background: #667eea;
    color: white;
    transform: translateY(-1px);
}

/* Filter buttons styling */
.filter-buttons {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
    justify-content: center;
}

.filter-btn {
    padding: 0.6rem 1.2rem !important;
    border-radius: 25px !important;
    font-size: 0.9rem !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
}

.filter-btn.btn-primary {
    background: #667eea !important;
    border-color: #667eea !important;
}

.filter-btn.btn-secondary {
    background: white !important;
    color: #667eea !important;
    border: 2px solid #e9ecef !important;
}

.filter-btn.btn-secondary:hover {
    border-color: #667eea !important;
    color: #667eea !important;
    background: #f8f9ff !important;
}

/* Search box styling */
.search-container input:focus {
    border-color: #667eea !important;
    outline: none !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
}

/* Section header improvements */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}
</style>

<!-- Page Header -->
<section class="section" style="background: #f8f9fa; padding: 2rem 0;">
    <div class="container">
        <h1 class="section-title">Current Projects Seeking Developers</h1>
        <p class="section-subtitle">Explore partnership opportunities and apply for projects that match your skills and interests</p>
    </div>
</section>

<!-- Search and Filter Section -->
<section class="section" style="padding: 1rem 0;">
    <div class="container">
        <div class="search-filter-bar" style="display: flex; gap: 2rem; margin-bottom: 1rem; flex-wrap: wrap; align-items: center; justify-content: center;">
            <div class="search-container" style="flex: 1; min-width: 300px; max-width: 500px;">
                <input type="text"
                       class="search-input"
                       data-target="projects"
                       placeholder="Search projects by title, description, or skills..."
                       value="<?php echo htmlspecialchars($search); ?>"
                       style="width: 100%; padding: 0.8rem 1.2rem; border: 2px solid #e9ecef; border-radius: 10px; font-size: 1rem; transition: border-color 0.3s ease;">
            </div>
            <div class="filter-buttons" style="display: flex; gap: 0.75rem; flex-wrap: wrap; justify-content: center;">
                <button class="filter-btn btn <?php echo $filter === 'all' ? 'btn-primary' : 'btn-secondary'; ?>"
                        data-filter="all" data-target="projects">
                    <i class="fas fa-th-large"></i> All Projects
                </button>
                <button class="filter-btn btn <?php echo $filter === 'web' ? 'btn-primary' : 'btn-secondary'; ?>"
                        data-filter="web" data-target="projects">
                    <i class="fas fa-code"></i> Web Development
                </button>
                <button class="filter-btn btn <?php echo $filter === 'mobile' ? 'btn-primary' : 'btn-secondary'; ?>"
                        data-filter="mobile" data-target="projects">
                    <i class="fas fa-mobile-alt"></i> Mobile Apps
                </button>
                <button class="filter-btn btn <?php echo $filter === 'ecommerce' ? 'btn-primary' : 'btn-secondary'; ?>"
                        data-filter="ecommerce" data-target="projects">
                    <i class="fas fa-shopping-cart"></i> E-commerce
                </button>
            </div>
        </div>
    </div>
</section>

<!-- Projects Grid -->
<section class="section" style="padding-top: 0;">
    <div class="container">
        <?php if (empty($projects)): ?>
            <div class="text-center" style="padding: 3rem 0;">
                <i class="fas fa-search" style="font-size: 3rem; color: #ccc; margin-bottom: 1rem;"></i>
                <h3>No Projects Found</h3>
                <p>There are currently no projects matching your criteria. Please check back later or adjust your search.</p>
                <a href="contact.php" class="btn btn-primary" style="margin-top: 1rem;">Contact Us for Updates</a>
            </div>
        <?php else: ?>
            <div class="projects-grid" id="projects-grid">
                <?php foreach ($projects as $project): ?>
                    <div class="card project-card" 
                         data-searchable="projects" 
                         data-filterable="projects"
                         data-category="<?php echo strtolower(strpos($project['title'], 'Mobile') !== false ? 'mobile' : (strpos($project['title'], 'E-commerce') !== false ? 'ecommerce' : 'web')); ?>">
                        
                        <div class="card-header">
                            <h3 class="card-title"><?php echo htmlspecialchars($project['title']); ?></h3>
                            <div class="card-meta">
                                <span class="project-status">
                                    <i class="fas fa-circle" style="color: #28a745; font-size: 0.5rem;"></i>
                                    Seeking Developers
                                </span>
                                <span style="margin-left: 1rem; color: #888;">
                                    <i class="fas fa-calendar"></i>
                                    Posted <?php echo format_date($project['created_at']); ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="card-content">
                            <p><?php echo truncate_text($project['description'], 120); ?></p>

                            <div class="partnership-terms">
                                <strong>Partnership Terms:</strong>
                                <p>
                                    <?php echo truncate_text($project['partnership_terms'], 80); ?>
                                </p>
                            </div>
                        </div>
                        
                        <div class="card-footer">
                            <button class="btn btn-primary apply-btn"
                                    onclick="openApplicationModal(<?php echo $project['id']; ?>, '<?php echo htmlspecialchars($project['title']); ?>')">
                                <i class="fas fa-paper-plane"></i> Apply Now
                            </button>
                            <a href="project-detail.php?id=<?php echo $project['id']; ?>" class="btn btn-secondary">
                                <i class="fas fa-eye"></i> View Details
                            </a>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Application Modal -->
<div id="application-modal" class="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div class="modal-content" style="background: white; margin: 5% auto; padding: 2rem; width: 90%; max-width: 600px; border-radius: 10px; position: relative;">
        <span class="close" onclick="closeApplicationModal()" style="position: absolute; top: 1rem; right: 1rem; font-size: 1.5rem; cursor: pointer;">&times;</span>
        
        <h2 id="modal-title">Apply for Project</h2>
        <p style="color: #666; margin-bottom: 2rem;">Fill out the form below to apply for this partnership opportunity.</p>
        
        <form id="application-form" class="application-form" onsubmit="submitApplicationForm(this); return false;">
            <input type="hidden" id="project-id" name="project_id">
            
            <div style="margin-bottom: 1rem;">
                <label for="applicant-name" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Full Name *</label>
                <input type="text" id="applicant-name" name="name" required 
                       style="width: 100%; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px;">
            </div>
            
            <div style="margin-bottom: 1rem;">
                <label for="applicant-email" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Email Address *</label>
                <input type="email" id="applicant-email" name="email" required 
                       style="width: 100%; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px;">
            </div>
            
            <div style="margin-bottom: 1rem;">
                <label for="applicant-country" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Country *</label>
                <select id="applicant-country" name="country" required 
                        style="width: 100%; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px;">
                    <option value="">Select your country</option>
                    <option value="Pakistan">Pakistan</option>
                    <option value="India">India</option>
                    <option value="Bangladesh">Bangladesh</option>
                    <option value="Sri Lanka">Sri Lanka</option>
                    <option value="Nepal">Nepal</option>
                    <option value="Other">Other</option>
                </select>
            </div>
            
            <div style="margin-bottom: 1rem;">
                <label for="experience-level" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Experience Level *</label>
                <select id="experience-level" name="experience_level" required 
                        style="width: 100%; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px;">
                    <option value="">Select experience level</option>
                    <option value="junior">Junior (1-2 years)</option>
                    <option value="mid">Mid-level (3-5 years)</option>
                    <option value="senior">Senior (5+ years)</option>
                    <option value="expert">Expert (10+ years)</option>
                </select>
            </div>
            
            <div style="margin-bottom: 1rem;">
                <label for="team-overview" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Team/Organization Overview *</label>
                <textarea id="team-overview" name="team_overview" required rows="3"
                          placeholder="Brief description of your development team/company..."
                          style="width: 100%; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px; resize: vertical;"></textarea>
            </div>

            <div style="margin-bottom: 1rem;">
                <label for="project-experience" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Previous Project Experience *</label>
                <textarea id="project-experience" name="project_experience" required rows="3"
                          placeholder="Examples of similar projects you've completed..."
                          style="width: 100%; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px; resize: vertical;"></textarea>
            </div>

            <div style="margin-bottom: 1rem;">
                <label for="development-approach" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Development Approach *</label>
                <textarea id="development-approach" name="development_approach" required rows="3"
                          placeholder="How you typically handle projects from start to finish..."
                          style="width: 100%; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px; resize: vertical;"></textarea>
            </div>

            <div style="margin-bottom: 1rem;">
                <label for="team-capabilities" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Team Size & Capabilities *</label>
                <textarea id="team-capabilities" name="team_capabilities" required rows="3"
                          placeholder="Size of your team and what you can deliver..."
                          style="width: 100%; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px; resize: vertical;"></textarea>
            </div>
            
            <div style="margin-bottom: 1rem;">
                <label for="portfolio-url" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Portfolio URL</label>
                <input type="url" id="portfolio-url" name="portfolio_url" 
                       placeholder="https://your-portfolio.com"
                       style="width: 100%; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px;">
            </div>
            
            <div style="margin-bottom: 1rem;">
                <label for="application-message" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Why are you interested in this project? *</label>
                <textarea id="application-message" name="message" required rows="4" 
                          placeholder="Tell us why you're the right fit for this project..."
                          style="width: 100%; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px; resize: vertical;"></textarea>
            </div>
            
            <div style="margin-bottom: 2rem;">
                <label for="cv-file" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Upload CV/Resume</label>
                <input type="file" id="cv-file" name="cv_file" accept=".pdf,.doc,.docx" 
                       style="width: 100%; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px;">
                <small style="color: #666;">Accepted formats: PDF, DOC, DOCX (Max 5MB)</small>
            </div>
            
            <div style="text-align: right;">
                <button type="button" onclick="closeApplicationModal()" class="btn btn-secondary" style="margin-right: 1rem;">Cancel</button>
                <button type="submit" class="btn btn-primary">Submit Application</button>
            </div>
        </form>
    </div>
</div>

<script>
function openApplicationModal(projectId, projectTitle) {
    document.getElementById('project-id').value = projectId;
    document.getElementById('modal-title').textContent = 'Apply for: ' + projectTitle;
    document.getElementById('application-modal').style.display = 'block';
    document.body.style.overflow = 'hidden';
}

function closeApplicationModal() {
    document.getElementById('application-modal').style.display = 'none';
    document.body.style.overflow = 'auto';
    document.getElementById('application-form').reset();
}



// Close modal when clicking outside
document.getElementById('application-modal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeApplicationModal();
    }
});

// Submit application form
function submitApplicationForm(form) {
    if (!validateForm(form)) return false;

    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;

    submitBtn.textContent = 'Submitting...';
    submitBtn.disabled = true;

    fetch('handlers/application.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('Application submitted successfully! We\'ll review it and get back to you.', 'success');
            form.reset();
            closeApplicationModal();
        } else {
            showMessage(data.message || 'An error occurred. Please try again.', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('An error occurred. Please try again.', 'error');
    })
    .finally(() => {
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    });

    return false;
}

// Validate form
function validateForm(form) {
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.style.borderColor = '#dc3545';
            isValid = false;
        } else {
            field.style.borderColor = '#ddd';
        }
    });

    // Validate email
    const emailField = form.querySelector('input[type="email"]');
    if (emailField && emailField.value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(emailField.value)) {
            emailField.style.borderColor = '#dc3545';
            isValid = false;
        }
    }

    return isValid;
}

// Show message
function showMessage(message, type) {
    // Remove existing messages
    const existingMessages = document.querySelectorAll('.alert-message');
    existingMessages.forEach(msg => msg.remove());

    // Create new message
    const messageDiv = document.createElement('div');
    messageDiv.className = `alert-message alert-${type}`;
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem 1.5rem;
        border-radius: 5px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        max-width: 400px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        ${type === 'success' ? 'background: #28a745;' : 'background: #dc3545;'}
    `;
    messageDiv.textContent = message;

    document.body.appendChild(messageDiv);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.remove();
        }
    }, 5000);
}
</script>

<?php include 'includes/footer.php'; ?>
