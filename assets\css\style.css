/* South Safari Website Styles */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1rem;
    color: #1a1a1a;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }

p {
    margin-bottom: 1rem;
    color: #555;
}

a {
    text-decoration: none;
    color: #333;
    transition: color 0.3s ease;
}

/* Header Styles */
.header {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.navbar {
    padding: 1rem 0;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: #1a1a1a;
}

.logo i {
    margin-right: 0.5rem;
    color: #666;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    font-weight: 500;
    padding: 0.5rem 0;
    position: relative;
    transition: color 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
    color: #1a1a1a;
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    right: 0;
    height: 2px;
    background: #333;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.whatsapp-btn {
    background: #25D366;
    color: white;
    padding: 0.5rem;
    border-radius: 50%;
    font-size: 1.2rem;
    transition: background 0.3s ease;
}

.whatsapp-btn:hover {
    background: #128C7E;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Main Content */
.main-content {
    margin-top: 80px;
    min-height: calc(100vh - 80px);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 4rem 0;
    text-align: center;
}

.hero h1 {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    color: #1a1a1a;
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    color: #666;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    display: inline-block;
    padding: 1rem 2rem;
    border-radius: 5px;
    font-weight: 600;
    text-align: center;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary {
    background: #1a1a1a;
    color: white;
}

.btn-primary:hover {
    background: #333;
    transform: translateY(-2px);
}

.btn-secondary {
    background: transparent;
    color: #1a1a1a;
    border: 2px solid #1a1a1a;
}

.btn-secondary:hover {
    background: #1a1a1a;
    color: white;
}

/* Section Styles */
.section {
    padding: 4rem 0;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.2rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* Grid Layouts */
.grid {
    display: grid;
    gap: 2rem;
}

.grid-2 { grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); }
.grid-3 { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); }
.grid-4 { grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); }

/* Card Styles */
.card {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.card-header {
    margin-bottom: 1rem;
}

.card-title {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
}

.card-meta {
    color: #888;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.card-content {
    margin-bottom: 1.5rem;
}

.card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Stats Section */
.stats {
    background: #1a1a1a;
    color: white;
    text-align: center;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.stat-item h3 {
    font-size: 2.5rem;
    color: white;
    margin-bottom: 0.5rem;
}

.stat-item p {
    color: #ccc;
}

/* Enhanced Footer Styles */
.footer-enhanced {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.footer-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(circle at 25% 25%, rgba(102, 126, 234, 0.1) 2px, transparent 2px), radial-gradient(circle at 75% 75%, rgba(118, 75, 162, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    pointer-events: none;
}

/* Newsletter Section */
.footer-newsletter {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 3rem 0;
    position: relative;
    z-index: 1;
}

.newsletter-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.newsletter-text h3 {
    color: white;
    font-size: 1.8rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.newsletter-text p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
    margin: 0;
}

.input-group {
    display: flex;
    background: white;
    border-radius: 50px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.input-group input {
    flex: 1;
    padding: 1rem 1.5rem;
    border: none;
    outline: none;
    font-size: 1rem;
    background: transparent;
}

.input-group input::placeholder {
    color: #999;
}

.btn-newsletter {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 1rem 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.1rem;
}

.btn-newsletter:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: scale(1.05);
}

/* Main Footer */
.footer-main {
    padding: 4rem 0 2rem;
    position: relative;
    z-index: 1;
}

.footer-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1.5fr;
    gap: 3rem;
}

.footer-column {
    position: relative;
}

/* Enhanced Brand Section */
.footer-brand-column {
    padding-right: 2rem;
}

.logo-enhanced {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    text-decoration: none;
}

.logo-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.logo-text {
    display: flex;
    flex-direction: column;
}

.logo-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    line-height: 1;
}

.logo-tagline {
    font-size: 0.9rem;
    color: #999;
    font-weight: 400;
}

.brand-description {
    color: #ccc;
    line-height: 1.6;
    margin-bottom: 2rem;
    font-size: 1rem;
}

/* Footer Stats */
.footer-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.8rem;
    color: #999;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Footer Titles */
.footer-title {
    color: white;
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
}

.footer-title::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    width: 30px;
    height: 2px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 1px;
}

/* Enhanced Footer Links */
.footer-links-enhanced {
    list-style: none;
    padding: 0;
}

.footer-links-enhanced li {
    margin-bottom: 0.75rem;
}

.footer-links-enhanced a {
    color: #ccc;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0;
    transition: all 0.3s ease;
    border-radius: 5px;
    position: relative;
}

.footer-links-enhanced a:hover {
    color: white;
    padding-left: 1rem;
    background: rgba(102, 126, 234, 0.1);
}

.footer-links-enhanced a i {
    width: 16px;
    font-size: 0.9rem;
    opacity: 0.7;
}

.apply-link {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    padding: 0.75rem 1rem !important;
    border-radius: 25px !important;
    margin-top: 0.5rem;
    font-weight: 500;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.apply-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    padding-left: 1rem !important;
}

/* Enhanced Contact Info */
.contact-info-enhanced {
    margin-bottom: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.contact-item:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateX(5px);
}

.contact-icon {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1rem;
    flex-shrink: 0;
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.contact-label {
    font-size: 0.8rem;
    color: #999;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.contact-value {
    color: white;
    font-weight: 500;
    text-decoration: none;
    transition: color 0.3s ease;
}

.contact-value:hover {
    color: #667eea;
}

/* Enhanced Social Links */
.social-section {
    margin-top: 2rem;
}

.social-title {
    color: white;
    font-size: 1rem;
    margin-bottom: 1rem;
    font-weight: 500;
}

.social-links-enhanced {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
}

.social-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    color: #ccc;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.social-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.social-link.whatsapp:hover {
    background: #25D366;
    color: white;
    border-color: #25D366;
}

.social-link.email:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.social-link.linkedin:hover {
    background: #0077B5;
    color: white;
    border-color: #0077B5;
}

.social-link.github:hover {
    background: #333;
    color: white;
    border-color: #333;
}

.social-link i {
    font-size: 1.1rem;
}

/* Enhanced Footer Bottom */
.footer-bottom-enhanced {
    background: rgba(0, 0, 0, 0.3);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 2rem 0;
    position: relative;
    z-index: 1;
}

.footer-bottom-content-enhanced {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 2rem;
    align-items: center;
}

.footer-bottom-left {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.copyright {
    color: #ccc;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.made-with {
    color: #999;
    margin: 0;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.footer-bottom-center {
    display: flex;
    justify-content: center;
}

.footer-badges {
    display: flex;
    gap: 1rem;
}

.badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(102, 126, 234, 0.1);
    border: 1px solid rgba(102, 126, 234, 0.2);
    border-radius: 20px;
    color: #667eea;
    font-size: 0.8rem;
    font-weight: 500;
}

.footer-bottom-right {
    display: flex;
    justify-content: flex-end;
}

.footer-bottom-links-enhanced {
    display: flex;
    gap: 1.5rem;
}

.footer-bottom-links-enhanced a {
    color: #999;
    text-decoration: none;
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    transition: color 0.3s ease;
}

.footer-bottom-links-enhanced a:hover {
    color: white;
}

.footer-bottom-links-enhanced a i {
    font-size: 0.8rem;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
    border-color: #dc3545;
}

.field-error {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Message Styles */
.message {
    position: fixed;
    top: 100px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 5px;
    color: white;
    font-weight: 500;
    z-index: 1000;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    max-width: 300px;
}

.message.show {
    transform: translateX(0);
}

.message-success {
    background: #28a745;
}

.message-error {
    background: #dc3545;
}

.message-info {
    background: #17a2b8;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
}

.modal-content .close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    transition: color 0.3s ease;
}

.modal-content .close:hover {
    color: #333;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: 1rem; }
.mb-2 { margin-bottom: 2rem; }
.mb-3 { margin-bottom: 3rem; }

.mt-1 { margin-top: 1rem; }
.mt-2 { margin-top: 2rem; }
.mt-3 { margin-top: 3rem; }
